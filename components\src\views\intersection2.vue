<template>
  <div>
    <!-- <h2>静态路口图</h2> -->
    <!-- <intersection-base-map
            ref="intersectionMap"
            :crossStatusData="crossStatusData"
            :agentId="agentId"
            :clickMode="true" /> -->

      <!-- <button @click="lockPhase" >相位锁定</button><br/>
      <button @click="unlockPhase" >解锁相位</button><br/>
      <button @click="changeControlPattern" >改变控制方案</button><br/>
      <button @click="getPhaseInfo" >获取相位参数信息</button><br/>
      <button @click="getControlInfo" >获取当前方案信息</button>
      <button @click="clearInterVals" >清除定时器</button> -->
    <!-- <el-button type="primary" @click="handleOpenConfigPanel" style="margin: 20px;">打开路口图面板</el-button>
    <el-dialog
      refs="intersectionMapDialog"
      class="abow_dialog"
      :width="dialogWidth"
      :visible.sync="boxVisible"
      :close-on-click-modal="false"
      @close="oncancle"
      append-to-body> -->
      <!-- <intersection-with-interface
        ref="intersection"
        :AgentId="agentId"
        :reqUrl="reqUrl"
        :Token="Token"
        :roadDirection="roadDirection"
        :modeName="modeName"
        :controlName="controlName"
        :isShowMode="isShowMode"
        :isShowInterval="isShowInterval"
        :isShowMessage ="isShowMessage"
        :isShowPatternStatus="isShowPatternStatus"
        :isShowIntersection="isShowIntersection"
        :isShowStages="isShowStages"
        @getTscControl="getTscControl"
        @registerMessage="registerMessage"
        @queryDevice="queryDevice"
        @onPhaseChange="onPhaseChange"
        @onSelectStages="onSelectStages">
      </intersection-with-interface> -->
    <!-- </el-dialog> -->
  <div class="layout-container2">
      <h2 style="color: #fff;">路口图</h2>
      <intersection-with-interface
        ref="intersection"
        :AgentId="agentId"
        :reqUrl="reqUrl"
        :Token="Token"
        :roadDirection="roadDirection"
        :modeName="modeName"
        :controlName="controlName"
        :isShowState="isShowState"
        :isShowMode="isShowMode"
        :isShowInterval="isShowInterval"
        :isShowMessage ="isShowMessage"
        :isShowPatternStatus="isShowPatternStatus"
        :isShowIntersection="isShowIntersection"
        :isShowStages="isShowStages"
        @getTscControl="getTscControl"
        @registerMessage="registerMessage"
        @queryDevice="queryDevice"
        @onPhaseChange="onPhaseChange"
        @onSelectStages="onSelectStages">
      </intersection-with-interface>
      <!-- <Stages :stagesList="stagesList"
              :currentStage="currentStage"
              @onSelectStages="onSelectStages"></Stages> -->

      <!-- <h2 style="color: #fff;">渠化图</h2>
      <channelization-with-interface
        ref="channelization"
        :AgentId="agentId"
        :reqUrl="reqUrl"
        :Token="Token"
        @getTscControl="getTscControl"
        @onPhaseChange="onPhaseChange"/>

    <h2 style="color: #fff;">流量统计渠化路口图</h2>
      <channelization-flow-statistic
      :AgentId="agentId"
      :phasesStatisticsList="phasesStatisticsList"
      bcgColor="#009900"
      :customText="customText"
      textFontSize="50"/> -->

    </div>
  </div>
</template>
<script>
import {
  setToken
} from '../utils/auth'
import IntersectionWithInterface from '../kisscomps/components/IntersectionWithInterface'
import ChannelizationWithInterface from '../kisscomps/components/ChannelizationWithInterface/ChannelizationWithInterface'
export default {
  name: 'demo',
  data () {
    return {
      roadDirection: 'left',
      // reqUrl: 'http://**************:10003/openatc',
      // agentId: '32050101041121000001',
      // agentId: '13013',
      agentId: '12007_390',
      // agentId: '12014',
      reqUrl: 'http://**************:10003/openatc',
      Token: 'eyJraWQiOiIxNjkzMjkyMzI0NzUyIiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiJhZG1pbiIsImV4cCI6NDA5MjEzNDQwMCwiaWF0IjoxNjkwODE5MjAwfQ.97bSPcUdh4xgAqkbw96Q-NfLVhFbgK0Yo1S8KYkSwz8',
      // agentId: '30003-352',
      // reqUrl: 'https://kints-dev.devdolphin.com/openatc',
      // Token: 'eyJraWQiOiIxNjUwNTA5MDI2ODk2IiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiJ4aWFvbWluZyIsImV4cCI6MTczNjkwOTAyNiwiaWF0IjoxNjUwNTA5MDI2fQ.-s4T-uMRmB2zf9yer87USKQXLY1a12Zq5lCOnqjNmfA',
      boxVisible: false,
      dialogWidth: '80%',
      modeName: '交警遥控',
      controlName: '步进',
      isShowState: true,
      isShowMode: true,
      isShowMessage: false,
      isShowPatternStatus: false,
      isShowIntersection: true,
      isShowInterval: true,
      isShowStages: true,
      customText: 'A',
      phasesStatisticsList: [
        {
          'phaseno': 1,
          'time': '2020-08-09 15:44:22',
          'phasestatistics': {
            'totalflow': 49,
            'averageflow': 12,
            'saturation': 1,
            'occupyrate': 69,
            'congestionindex': 'A',
            'greenusage': 10
          }
        },
        {
          'phaseno': 2,
          'time': '2020-08-09 15:44:22',
          'phasestatistics': {
            'totalflow': 62,
            'averageflow': 31,
            'saturation': 3,
            'occupyrate': 98,
            'congestionindex': 'F',
            'greenusage': 10
          }
        }
      ],
      crossStatusData: {}
    }
  },
  components: {
    IntersectionWithInterface,
    ChannelizationWithInterface
  },
  methods: {
    oncancle () {
      this.boxVisible = false
    },
    handleOpenConfigPanel () {
      this.boxVisible = true
    },
    setDialogWidth () {
      var val = document.body.offsetWidth
      const def = 1200 // 默认宽度
      if (val < def) {
        this.dialogWidth = '80%'
      } else {
        this.dialogWidth = def + 'px'
      }
    },
    async lockPhase (reqData) {
      console.log('lock')
      reqData = {
        'mode': 0,
        'control': 4,
        'terminal': 1,
        'delay': null,
        'duration': null,
        'value': 1
      }
      let res = await this.$refs.intersection.lockPhase(reqData)
      console.log(res)
      console.log(reqData)
    },
    async unlockPhase (reqData) {
      console.log('unlock')
      reqData = {
        'mode': 0,
        'control': 0,
        'terminal': 1,
        'delay': null,
        'duration': null,
        'value': 0
      }
      let res = await this.$refs.intersection.unlockPhase(reqData)
      console.log(res)
      console.log(reqData)
    },
    async changeControlPattern (reqData) {
      console.log('changeControlPattern')
      reqData = {
        'mode': 0,
        'control': 1,
        'terminal': 1,
        'delay': null,
        'duration': null,
        'value': 0
      }
      let res = await this.$refs.intersection.changeControlPattern(reqData)
      console.log(res)
      console.log(reqData)
    },
    async getPhaseInfo () {
      console.log('getPhaseInfo')
      let res = await this.$refs.intersection.getPhaseInfo()
      console.log(res)
      return res
    },
    async getControlInfo () {
      console.log('getControlInfo')
      let res = await this.$refs.intersection.getControlInfo()
      console.log(res)
      return res
    },
    onPhaseChange (res, index) {
      console.log('onPhaseChange:')
      console.log(res, index)
      this.stagesList = res
      // this.currentStage = index
    },
    getTscControl (res) {
      console.log('getTscControl:', res)
      // let control = res.data.data.control
      // let mode = res.data.data.mode
      // console.log("control,mode:",control,mode)
      // this.controlName = control
      // this.modeName = mode
    },
    registerMessage (res) {
      console.log('registerMessage:', res)
    },
    queryDevice (res) {
      console.log('queryDevice:', res)
    },
    clearInterVals () {
      this.$refs.intersection.clearInterVals()
    },
    onSelectStages (res) {
      console.log('onSelectStages')
      console.log(res)
    },
    setPropsToken (token) {
      // 获取组件外传入的token，便于独立组件调用接口
      if (token && token !== '') {
        setToken(token)
      }
    },
    handleClickCrossIcon (allChoosedDir, curClickedPhase) {
      console.log('handleClickCrossIcon', allChoosedDir, curClickedPhase)
    }
  },
  created () {
    this.setDialogWidth()
  },
  mounted () {
    // let _this = this
    // setTimeout(function () {
    //   alert('Hello')
    //   _this.agentId = 'scats1'
    // }, 5 * 1000)
    this.setPropsToken(this.Token)
    window.onresize = () => {
      return (() => {
        this.setDialogWidth()
      })()
    }
  },
  destroyed () {
  }
}
</script>
<style lang='scss'>
.layout-container {
  width: 800PX;
}
.layout-container1 {
  width: 242PX;
  height: 176PX;
  border: 1PX solid green;
}
.layout-container2 {
  /* height: 600px; */
  width: 1020px;
  border: 1PX solid yellow;
  position: relative;
}
.abow_dialog {
    display: flex;
    justify-content: center;
    align-items: Center;
    overflow: hidden;
    .el-dialog {
        margin: 0 auto !important;
        height: 90%;
        overflow: hidden;
        .el-dialog__body {
            position: absolute;
            left: 0;
            top: 54px;
            bottom: 0;
            right: 0;
            padding: 0;
            z-index: 1;
            overflow-y: auto;
            overflow-x: auto;
        }
    }
}
</style>
<style lang="scss" scoped>
  .text {
    color: #fff;
  }
</style>
