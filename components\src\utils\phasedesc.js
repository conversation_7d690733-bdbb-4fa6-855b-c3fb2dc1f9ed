/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
/**
 * @Description: 根据相位的描述id获取对应的描述名字
 * @Author: yangdongyang
 * @Date: Create in 13:41 2019/3/26
 * @Modified By:
 */
export const images = [{
  id: 1,
  zname: '东直行',
  ename: 'East-Straight',
  class: 'iconfont icon-dongzhihang'
},
{
  id: 2,
  zname: '东左转',
  ename: 'East-Left',
  class: 'iconfont icon-dongzuozhuan'
},
{
  id: 3,
  zname: '东右转',
  ename: 'East-Right',
  class: 'iconfont icon-dongyouzhuan'
},
{
  id: 4,
  zname: '东掉头',
  ename: 'East-Back',
  class: 'iconfont icon-dongdiaotou',
  leftclass: 'iconfont icon-dongdiaotouzuohang'
},
{
  id: 5,
  zname: '西直行',
  ename: 'West-Straight',
  class: 'iconfont icon-xizhihang'
},
{
  id: 6,
  zname: '西左转',
  ename: 'West-Left',
  class: 'iconfont icon-xizuozhuan'
},
{
  id: 7,
  zname: '西右转',
  ename: 'West-Right',
  class: 'iconfont icon-xiyouzhuan'
},
{
  id: 8,
  zname: '西掉头',
  ename: 'West-Back',
  class: 'iconfont icon-xidiaotou',
  leftclass: 'iconfont icon-xidiaotouzuohang'
},
{
  id: 9,
  zname: '北直行',
  ename: 'North-Straight',
  class: 'iconfont icon-beizhihang'
},
{
  id: 10,
  zname: '北左转',
  ename: 'North-Left',
  class: 'iconfont icon-beizuozhuan'
},
{
  id: 11,
  zname: '北右转',
  ename: 'North-Right',
  class: 'iconfont icon-beiyouzhuan'
},
{
  id: 12,
  zname: '北掉头',
  ename: 'North-Back',
  class: 'iconfont icon-beidiaotou',
  leftclass: 'iconfont icon-beidiaotouzuohang'
},
{
  id: 13,
  zname: '南直行',
  ename: 'South-Straight',
  class: 'iconfont icon-nanzhihang'
},
{
  id: 14,
  zname: '南左转',
  ename: 'South-Left',
  class: 'iconfont icon-nanzuozhuan'
},
{
  id: 15,
  zname: '南右转',
  ename: 'South-Right',
  class: 'iconfont icon-nanyouzhuan'
},
{
  id: 16,
  zname: '南掉头',
  ename: 'South-Back',
  class: 'iconfont icon-nandiaotou',
  leftclass: 'iconfont icon-nandiaotouzuohang'
}]

export function getPhaseDesc (list, language) {
  // let language = this.$i18n.locale
  // console.log(language)
  let str = ''
  if (language === 'en') {
    for (let ll of list) {
      for (let image of images) {
        if (image.id === ll) {
          str = str + ',' + image.ename
        }
      }
    }
  } else {
    for (let ll of list) {
      for (let image of images) {
        if (image.id === ll) {
          str = str + ',' + image.zname
        }
      }
    }
  }
  if (str !== '') {
    str = str.substr(1)
  }
  return str
}
