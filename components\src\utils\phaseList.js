/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
export const images = [{
  id: 1,
  // name: 'East-Straight',
  name: 'openatccomponents.overview.es',
  // img: require('./images/East-Straight.svg'),
  class: 'iconfont icon-dongzhihang'
},
{
  id: 2,
  // name: 'East-Left',
  name: 'openatccomponents.overview.el',
  // img: require('./images/East-Left.svg'),
  class: 'iconfont icon-beizuozhuan'
},
{
  id: 3,
  // name: 'East-Right',
  name: 'openatccomponents.overview.er',
  // img: require('./images/East-Right.svg'),
  class: 'iconfont icon-dongyouzhuan'
},
{
  id: 4,
  // name: 'East-Back',
  name: 'openatccomponents.overview.eb',
  // img: require('./images/East-Back.svg'),
  class: 'iconfont icon-dongdiaotou'
},
{
  id: 5,
  // name: 'West-Straight',
  name: 'openatccomponents.overview.ws',
  // img: require('./images/West-Straight.svg'),
  class: 'iconfont icon-xizhihang'
},
{
  id: 6,
  // name: 'West-Left',
  name: 'openatccomponents.overview.wl',
  // img: require('./images/West-Left.svg'),
  class: 'iconfont icon-xizuozhuan'
},
{
  id: 7,
  // name: 'West-Right',
  name: 'openatccomponents.overview.wr',
  // img: require('./images/West-Right.svg'),
  class: 'iconfont icon-xiyouzhuan'
},
{
  id: 8,
  // name: 'West-Back',
  name: 'openatccomponents.overview.wb',
  // img: require('./images/West-Back.svg'),
  class: 'iconfont icon-xidiaotou'
},
{
  id: 9,
  // name: 'North-Straight',
  name: 'openatccomponents.overview.ns',
  // img: require('./images/North-Straight.svg'),
  class: 'iconfont icon-beizhihang'
},
{
  id: 10,
  // name: 'North-Left',
  name: 'openatccomponents.overview.nl',
  // img: require('./images/North-Left.svg'),
  class: 'iconfont icon-beizuozhuan'
},
{
  id: 11,
  // name: 'North-Right',
  name: 'openatccomponents.overview.nr',
  // img: require('./images/North-Right.svg'),
  class: 'iconfont icon-beiyouzhuan'
},
{
  id: 12,
  // name: 'North-Back',
  name: 'openatccomponents.overview.nb',
  // img: require('./images/North-Back.svg'),
  class: 'iconfont icon-beidiaotou'
},
{
  id: 13,
  // name: 'South-Straight',
  name: 'openatccomponents.overview.ss',
  // img: require('./images/South-Straight.svg'),
  class: 'iconfont icon-nanzhihang'
},
{
  id: 14,
  // name: 'South-Left',
  name: 'openatccomponents.overview.sl',
  // img: require('./images/South-Left.svg'),
  class: 'iconfont icon-nanzuozhuan'
},
{
  id: 15,
  // name: 'South-Right',
  name: 'openatccomponents.overview.sr',
  // img: require('./images/South-Right.svg'),
  class: 'iconfont icon-nanyouzhuan'
},
{
  id: 16,
  // name: 'South-Back',
  name: 'openatccomponents.overview.sb',
  // img: require('./images/South-Back.svg'),
  class: 'iconfont icon-nandiaotou'
},
{
  id: 17,
  name: 'openatccomponents.overview.ses',
  class: 'iconfont icon-dongnanzhihang'
},
{
  id: 18,
  name: 'openatccomponents.overview.sel',
  class: 'iconfont icon-dongnanzuozhuan'
},
{
  id: 19,
  name: 'openatccomponents.overview.ser',
  class: 'iconfont icon-dongnanyouzhuan'
},
{
  id: 20,
  name: 'openatccomponents.overview.seb',
  class: 'iconfont icon-dongnandiaotou'
},
{
  id: 21,
  name: 'openatccomponents.overview.sws',
  class: 'iconfont icon-xinanzhihang'
},
{
  id: 22,
  name: 'openatccomponents.overview.swl',
  class: 'iconfont icon-xibeizuozhuan'
},
{
  id: 23,
  name: 'openatccomponents.overview.swr',
  class: 'iconfont icon-xibeiyouzhuan'
},
{
  id: 24,
  name: 'openatccomponents.overview.swb',
  class: 'iconfont icon-xinandiaotou'
},
{
  id: 25,
  name: 'openatccomponents.overview.nes',
  class: 'iconfont icon-dongbeizhihang'
},
{
  id: 26,
  name: 'openatccomponents.overview.nel',
  class: 'iconfont icon-dongbeizuozhuan'
},
{
  id: 27,
  name: 'openatccomponents.overview.ner',
  class: 'iconfont icon-dongbeiyouzhuan'
},
{
  id: 28,
  name: 'openatccomponents.overview.neb',
  class: 'iconfont icon-dongnandiaotou'
},
{
  id: 29,
  name: 'openatccomponents.overview.nws',
  class: 'iconfont icon-xibeizhihang'
},
{
  id: 30,
  name: 'openatccomponents.overview.nwl',
  class: 'iconfont icon-xibeizuozhuan'
},
{
  id: 31,
  name: 'openatccomponents.overview.nwr',
  class: 'iconfont icon-xibeiyouzhuan'
},
{
  id: 32,
  name: 'openatccomponents.overview.nwb',
  class: 'iconfont icon-xibeidiaotou'
}]
