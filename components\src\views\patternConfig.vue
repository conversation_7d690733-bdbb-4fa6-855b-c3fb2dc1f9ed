<!-- eslint-disable -->
<template>
  <div>
      <button @click="getSaveParam" >获取相位参数信息</button><br/>
      <div class="layout-container2">
        <PatternConfig
          :agentids="agentids7"
          :opt="opt7"
          :isShowTableHeader="isShowTableHeader"
          :tableColums="tableColums"
          :isSingleCrossMode="isSingleCrossMode"
          :isAllowedExpand="isAllowedExpand"
          :isOnlyOneTab="isOnlyOneTab"
          :isShowSingle="isShowSingle"
          ref="patternConfig"
        >
        </PatternConfig>
        <!-- <el-table :data="opt7.optPatternList[0].devs" max-height="600" id="footerBtn" :show-header="true">
        <el-table-column align="center" label="No" width="45" minWidth="40">
          <template slot-scope="scope">
            <span>{{scope.$index+1}}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('openatccomponents.greenwaveoptimize.deviceid')" width="100" minWidth="40">
          <template slot-scope="scope">
            <span>{{scope.row.agentid}}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" :label="$t('openatccomponents.pattern.plan')">
          <template slot-scope="scope">
            <div class="pattern-figure">
              <pattern-list v-if="scope.row.feature.patternList[0].rings.length > 0"
                :contrloType="rings"
                :patternStatusList="scope.row.feature.patternList[0].rings"
                :cycles="scope.row.feature.patternList[0].cycle"
                :phaseList="scope.row.feature.phaseList"
                :showBarrier="true">
              </pattern-list>
              <pattern-list
                v-else
                :patternId="scope.row.patternid"
                :contrloType="stage"
                :index="scope.$index"
                :cycleChange="false"
                :stagesChange="scope.row.feature.patternList[0].stagesList"
                :patternStatusList="scope.row.feature.patternList[0].rings"
                :patternList="scope.row.feature.patternList"
                :allPatternList="scope.row.feature.patternList"
                :cycles="scope.row.feature.patternList[0].cycle"
                :phaseList="scope.row.feature.phaseList"
                :agentId="scope.row.agentid"
                :showBarrier="false">
              </pattern-list>
            </div>
          </template>
        </el-table-column>
        </el-table> -->

    </div>
  </div>
</template>
/* eslint-disable */
<script>
import {
  setToken
} from '../utils/auth'
// import PatternConfig from '../kisscomps/components/PatternConfig'
export default {
  name: 'PatternConfigTest',
  // components: {
  //   PatternConfig
  // },
  data () {
    return {
      tableColums: ['agentid', 'name', 'period', 'pattern'], // greenWave, period
      // tableColums: ['no', 'id', 'period', 'name', 'isUsed', 'patternId', 'offset', 'cycle', 'pattern'], // greenWave, period
      isShowSingle: false,
      isOnlyOneTab: false,
      isSingleCrossMode: false,
      isShowTableHeader: true,
      isAllowedExpand: false,
      // isAllowedExpand: true,
      // agentids: ['gjnlgjxl'],
      agentids4: [
        'tjblxyl',
        'jmlfql',
        'tjbllyl',
        'tjblfql',
        'jmlytl',
        'tjbljml',
        'gjnljml',
        'gjnlahql',
        'tjblgjxl',
        'gjnlgjxl',
        'tjblsxl'
      ],
      opt4: {
        optPatternList: [
          {
            name: '方案111',
            devs: [
              {
                agentid: 'tjblgjxl',
                patternid: 1,
                isused: true
              },
              {
                agentid: 'gjnlahql',
                patternid: 1,
                isused: true
              },
              {
                agentid: 'gjnljml',
                patternid: 1,
                isused: true
              },
              {
                agentid: 'tjblxyl',
                patternid: 1,
                isused: false
              },
              {
                agentid: 'jmlfql',
                patternid: 1,
                isused: false
              },
              {
                agentid: 'tjbllyl',
                patternid: 1,
                isused: false
              },
              {
                agentid: 'tjblfql',
                patternid: 1,
                isused: false
              },
              {
                agentid: 'jmlytl',
                patternid: 1,
                isused: false
              },
              {
                agentid: 'tjbljml',
                patternid: 1,
                isused: false
              },
              {
                agentid: 'gjnlgjxl',
                patternid: 1,
                isused: false
              },
              {
                agentid: 'tjblsxl',
                patternid: 1,
                isused: false
              }
            ]
          }
        ]
      },
      agentids3: ['13003', '13006', '13007', '13013'],
      // agentids2: ['15001', '15001', '15003', '15004', '15005'],
      roadDirection: 'right',
      // reqUrl: 'http://192.168.13.103:10003/openatc',
      agentId: '12008_835',
      // agentId: '0351-01',
      reqUrl: 'http://192.168.13.103:10003/openatc',
      Token135: 'eyJraWQiOiIxNzIzMDgzMDcxMzIxIiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTk4NTQ0MzIwMCwiaWF0IjoxNzIyNDM4MDAwfQ.i-RVHcxmtgvD9boQECpBCz7T1fJ0TAjzO5BWin3VdoY',
      Token: 'eyJraWQiOiIxNzAwMjAwNDQ4MTA0IiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTc2NzExMDQwMCwiaWF0IjoxNjk5Mjg2NDAwfQ.AyVJCGmVQVafdWBDQ7TMhVQ5Dvjy13NeuQ8rimTPgtc',
      // reqUrl: 'http://**************:10003/openatc',
      // Token: 'eyJraWQiOiIxNjkxMzcxMTc5ODcxIiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTcxNDE0NzIwMCwiaWF0IjoxNjkwODE5MjAwfQ.AhUj9taw4_IpP77AmP5bvCE87dDQ4-ZdsEJzrlWXKF8',
      // agentId: '30003-352',
      // reqUrl: 'https://kints-dev.devdolphin.com/openatc',
      // Token: 'eyJraWQiOiIxNjUwNTA5MDI2ODk2IiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiJ4aWFvbWluZyIsImV4cCI6MTczNjkwOTAyNiwiaWF0IjoxNjUwNTA5MDI2fQ.-s4T-uMRmB2zf9yer87USKQXLY1a12Zq5lCOnqjNmfA',
      boxVisible: false,
      opt3: {
        'optPatternList': [
          {
            'name': '杰斯安的房间里 1',
            'devs': [
              {
                'agentid': 'gjnlahql',
                'patternid': 1,
                'isused': true
              },
              {
                'agentid': 'xylsnl',
                'patternid': 1,
                'isused': true
              },
              {
                'agentid': 'cslyll',
                'patternid': 2,
                'isused': false
              }
            ]
          }
        ]
      },
      agentids6: ['tjblsxl-ticp6'],
      agentids5: ['tjllybj', 'tjlzsdj', 'tjlhaj', 'tjlyhddj'],
      opt5: {
          'optPatternList': [
              {
                  'name': '仿真实例 1',
                  'devs': [
                      {
                          'agentid': 'tjllybj',
                          'patternid': 1,
                          'isused': true
                      },
                      {
                          'agentid': 'tjlzsdj',
                          'patternid': 1,
                          'isused': true
                      },
                      {
                          'agentid': 'tjlhaj',
                          'patternid': 1,
                          'isused': true
                      },
                      {
                          'agentid': 'tjlyhddj',
                          'patternid': 1,
                          'isused': true
                      }
                  ]
              }
          ]
      },
      agentids: ['gjnlgjxl'],
      opt: {
        'optPatternList': [
          {
            'id': 1,
            'name': 'dsfasdfdsf',
            'tabName': '1',
            'devs': [
              {
                'agentid': 'gjnlgjxl',
                'patternid': 1,
                'period': ['09:30', '11:30'],
                'isused': true,
                'desc': '方案1',
                'id': 1,
                'offset': 0,
                'cycle': 132,
                'patternId': 2,
                'patternDesc': '方案1',
                'feature': {
                  'patternList': [
                    {
                      'id': 1,
                      'desc': '方案1',
                      'offset': 0,
                      'cycle': 90,
                      'contrloType': 'ring',
                      'rings': [
                        [
                          {
                            'name': '相位1',
                            'id': 1,
                            'value': 40,
                            'mode': 2,
                            'delaystart': 0,
                            'advanceend': 0,
                            'flowperhour': 0,
                            'saturation': 1700,
                            'desc': [
                              {
                                'id': 1
                              },
                              {
                                'id': 5
                              }
                            ]
                          },
                          {
                            'name': '相位2',
                            'id': 2,
                            'value': 30,
                            'mode': 2,
                            'delaystart': 0,
                            'advanceend': 0,
                            'flowperhour': 0,
                            'saturation': 1700,
                            'desc': [
                              {
                                'id': 2
                              },
                              {
                                'id': 6
                              }
                            ]
                          },
                          {
                            'name': '相位3',
                            'id': 3,
                            'value': 20,
                            'mode': 2,
                            'delaystart': 0,
                            'advanceend': 0,
                            'flowperhour': 0,
                            'saturation': 1700,
                            'desc': [
                              {
                                'id': 14
                              }
                            ]
                          }
                        ],
                        [],
                        [],
                        []
                      ],
                      'stages': [
                        [
                          1
                        ],
                        [
                          2
                        ],
                        [
                          3
                        ]
                      ],
                      'stagesList': [
                        {
                          'key': 0,
                          'green': 0,
                          'yellow': 0,
                          'red': 0,
                          'split': 30,
                          'stageSplit': 0,
                          'phases': [
                            1
                          ]
                        },
                        {
                          'key': 1,
                          'green': 0,
                          'yellow': 0,
                          'red': 0,
                          'split': 30,
                          'stageSplit': 0,
                          'phases': [
                            2
                          ]
                        },
                        {
                          'key': 2,
                          'green': 0,
                          'yellow': 0,
                          'red': 0,
                          'split': 20,
                          'stageSplit': 0,
                          'phases': [
                            3
                          ]
                        }
                      ],
                      'barriers': []
                    }
                  ],
                  'phases': [
                      {
                        'id': 1,
                        'flowperhour': 200,
                        'saturation': 1701
                      },
                      {
                        'id': 3,
                        'flowperhour': 100,
                        'saturation': 1703
                      },
                      {
                        'id': 6,
                        'flowperhour': 100,
                        'saturation': 1700
                      },
                      {
                        'id': 2,
                        'flowperhour': 100,
                        'saturation': 1702
                      },
                      {
                        'id': 4,
                        'flowperhour': 100,
                        'saturation': 1700
                      },
                      {
                        'id': 5,
                        'flowperhour': 100,
                        'saturation': 1700
                      }
                  ]
                }
              }
            ]
          }
        ]
      },
      agentids7: [
            'cslhsl',
            'cslhsl',
            'cslhsl',
            'cslhsl',
            'cslhsl',
            'cslhsl'
      ],
      opt7: {
'optPatternList': [
{
'id': 1,
'name': '优化方案',
'tabName': '1',
'devs': [
{
'agentid': 'cslhsl',
'daterange': [
'2024-10-01',
'2024-10-09'
],
'days': [
6,
7,
1,
2,
3,
4,
5
],
'feature': {
'dateList': [
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 1',
'id': 11,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 11
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 2',
'id': 13,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 13
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 3',
'id': 14,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 14
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调绿波上行',
'id': 12,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 12
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-green-down',
'id': 10,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 10
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '最新测试1-green-up',
'id': 9,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 9
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': 'null-green-down',
'id': 8,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 8
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1-green-down',
'id': 6,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 6
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-green-up',
'id': 7,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 7
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-1-green-down',
'id': 5,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 5
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2',
'id': 4,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 4
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1',
'id': 3,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 3
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '下行全天',
'id': 2,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 2
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '日期1',
'id': 1,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 1
}
],
'patternList': [
{
'barriers': [],
'cycle': 39,
'desc': '方案1',
'id': 1,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 5
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 6
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 8
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '下行全天-green-down',
'id': 2,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 1-green-down',
'id': 3,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-down',
'id': 4,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-1-green-down',
'id': 5,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 115,
'desc': '方案 1-green-up',
'id': 6,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 28
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 29
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-up',
'id': 7,
'offset': 24,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': 'null-green-down',
'id': 8,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '最新测试1-green-up',
'id': 9,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-green-down',
'id': 10,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 1',
'id': 11,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调绿波上行',
'id': 12,
'offset': 21,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 2',
'id': 13,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调方案 3',
'id': 14,
'offset': 109,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
}
],
'patternPhaseMap': null,
'phaseList': [
{
'concurrent': [],
'controltype': 0,
'direction': [
1,
5
],
'flashgreen': 3,
'greenpulse': 5,
'id': 1,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
2,
6
],
'flashgreen': 3,
'greenpulse': 5,
'id': 2,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
9,
13
],
'flashgreen': 3,
'greenpulse': 5,
'id': 3,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
10,
14
],
'flashgreen': 3,
'greenpulse': 5,
'id': 4,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
}
],
'phases': [
{
'flowperhour': 99,
'icfsoLaneStat': {
'directionstr': null,
'flow': 699.5,
'hourlyequivalent': 99,
'laneid': 'cslhsl-1-2',
'seconds': 25200,
'stattime': '00:00:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 24,
'icfsoLaneStat': {
'directionstr': null,
'flow': 170.5,
'hourlyequivalent': 24,
'laneid': 'cslhsl-3-1',
'seconds': 25200,
'stattime': '00:00:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 28,
'icfsoLaneStat': {
'directionstr': null,
'flow': 199.33333333333335,
'hourlyequivalent': 28,
'laneid': 'cslhsl-4-2',
'seconds': 25200,
'stattime': '00:00:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 42,
'icfsoLaneStat': {
'directionstr': null,
'flow': 302.8333333333333,
'hourlyequivalent': 42,
'laneid': 'cslhsl-4-1',
'seconds': 25200,
'stattime': '00:00:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'planList': [
{
'desc': '计划1',
'id': 1,
'index': null,
'plan': [
{
'control': 5,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 1
},
{
'control': 5,
'hour': 0,
'id': 2,
'minute': 4,
'pattern': 1
}
]
},
{
'desc': '下行全天',
'id': 2,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 2
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 55,
'pattern': 0
}
]
},
{
'desc': '方案 1',
'id': 3,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2',
'id': 4,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 4
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2-1-green-down',
'id': 5,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 5
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 1-green-down',
'id': 6,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '方案 2-green-up',
'id': 7,
'index': null,
'plan': [
{
'control': 10,
'hour': 10,
'id': 1,
'minute': 0,
'pattern': 7
},
{
'control': 0,
'hour': 16,
'id': 2,
'minute': 15,
'pattern': 0
}
]
},
{
'desc': 'null-green-down',
'id': 8,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 8
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '最新测试1-green-up',
'id': 9,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 9
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-green-down',
'id': 10,
'index': null,
'plan': [
{
'control': 10,
'hour': 7,
'id': 1,
'minute': 15,
'pattern': 10
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 1',
'id': 11,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 11
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-协调绿波上行',
'id': 12,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 12
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 2',
'id': 13,
'index': null,
'plan': [
{
'control': 10,
'hour': 12,
'id': 1,
'minute': 0,
'pattern': 13
},
{
'control': 0,
'hour': 14,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 3',
'id': 14,
'index': null,
'plan': [
{
'control': 10,
'hour': 13,
'id': 1,
'minute': 0,
'pattern': 14
},
{
'control': 0,
'hour': 15,
'id': 2,
'minute': 0,
'pattern': 0
}
]
}
]
},
'fixedtimePlan': {
'agentid': null,
'barriers': [],
'cycle': 39,
'offset': 0,
'phase': [
{
'adjusted': false,
'duration': 20,
'flowperhour': 99,
'id': 1,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.5526315789473685
},
{
'adjusted': false,
'duration': 5,
'flowperhour': 24,
'id': 2,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.10526315789473684
},
{
'adjusted': false,
'duration': 6,
'flowperhour': 28,
'id': 3,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.13157894736842105
},
{
'adjusted': false,
'duration': 8,
'flowperhour': 42,
'id': 4,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.21052631578947368
}
],
'rings': [
{
'num': 1,
'sequence': [
1,
2,
3,
4
],
'y': 0
}
]
},
'name': 'cslhsl',
'patternid': 1,
'phaseflows': [
{
'flowperhour': 99,
'icfsoLaneStat': {
'directionstr': null,
'flow': 699.5,
'hourlyequivalent': 99,
'laneid': 'cslhsl-1-2',
'seconds': 25200,
'stattime': '00:00:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 24,
'icfsoLaneStat': {
'directionstr': null,
'flow': 170.5,
'hourlyequivalent': 24,
'laneid': 'cslhsl-3-1',
'seconds': 25200,
'stattime': '00:00:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 28,
'icfsoLaneStat': {
'directionstr': null,
'flow': 199.33333333333335,
'hourlyequivalent': 28,
'laneid': 'cslhsl-4-2',
'seconds': 25200,
'stattime': '00:00:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 42,
'icfsoLaneStat': {
'directionstr': null,
'flow': 302.8333333333333,
'hourlyequivalent': 42,
'laneid': 'cslhsl-4-1',
'seconds': 25200,
'stattime': '00:00:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'timeperiodrange': [
'00:00:00',
'07:00:00'
],
'unitid': 'cslhsl'
},
{
'agentid': 'cslhsl',
'daterange': [
'2024-10-01',
'2024-10-09'
],
'days': [
6,
7,
1,
2,
3,
4,
5
],
'feature': {
'dateList': [
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 1',
'id': 11,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 11
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 2',
'id': 13,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 13
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 3',
'id': 14,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 14
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调绿波上行',
'id': 12,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 12
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-green-down',
'id': 10,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 10
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '最新测试1-green-up',
'id': 9,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 9
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': 'null-green-down',
'id': 8,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 8
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1-green-down',
'id': 6,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 6
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-green-up',
'id': 7,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 7
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-1-green-down',
'id': 5,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 5
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2',
'id': 4,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 4
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1',
'id': 3,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 3
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '下行全天',
'id': 2,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 2
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '日期1',
'id': 1,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 1
}
],
'patternList': [
{
'barriers': [],
'cycle': 39,
'desc': '方案1',
'id': 1,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 22
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 4
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 6
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 7
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '下行全天-green-down',
'id': 2,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 1-green-down',
'id': 3,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-down',
'id': 4,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-1-green-down',
'id': 5,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 115,
'desc': '方案 1-green-up',
'id': 6,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 28
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 29
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-up',
'id': 7,
'offset': 24,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': 'null-green-down',
'id': 8,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '最新测试1-green-up',
'id': 9,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-green-down',
'id': 10,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 1',
'id': 11,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调绿波上行',
'id': 12,
'offset': 21,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 2',
'id': 13,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调方案 3',
'id': 14,
'offset': 109,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
}
],
'patternPhaseMap': null,
'phaseList': [
{
'concurrent': [],
'controltype': 0,
'direction': [
1,
5
],
'flashgreen': 3,
'greenpulse': 5,
'id': 1,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
2,
6
],
'flashgreen': 3,
'greenpulse': 5,
'id': 2,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
9,
13
],
'flashgreen': 3,
'greenpulse': 5,
'id': 3,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
10,
14
],
'flashgreen': 3,
'greenpulse': 5,
'id': 4,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
}
],
'phases': [
{
'flowperhour': 108,
'icfsoLaneStat': {
'directionstr': null,
'flow': 108.66666666666667,
'hourlyequivalent': 108,
'laneid': 'cslhsl-3-3',
'seconds': 3600,
'stattime': '07:00:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 17,
'icfsoLaneStat': {
'directionstr': null,
'flow': 17,
'hourlyequivalent': 17,
'laneid': 'cslhsl-3-1',
'seconds': 3600,
'stattime': '07:00:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 27,
'icfsoLaneStat': {
'directionstr': null,
'flow': 27,
'hourlyequivalent': 27,
'laneid': 'cslhsl-4-2',
'seconds': 3600,
'stattime': '07:00:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 36,
'icfsoLaneStat': {
'directionstr': null,
'flow': 36.166666666666667,
'hourlyequivalent': 36,
'laneid': 'cslhsl-4-1',
'seconds': 3600,
'stattime': '07:00:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'planList': [
{
'desc': '计划1',
'id': 1,
'index': null,
'plan': [
{
'control': 5,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 1
},
{
'control': 5,
'hour': 0,
'id': 2,
'minute': 4,
'pattern': 1
}
]
},
{
'desc': '下行全天',
'id': 2,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 2
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 55,
'pattern': 0
}
]
},
{
'desc': '方案 1',
'id': 3,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2',
'id': 4,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 4
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2-1-green-down',
'id': 5,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 5
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 1-green-down',
'id': 6,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '方案 2-green-up',
'id': 7,
'index': null,
'plan': [
{
'control': 10,
'hour': 10,
'id': 1,
'minute': 0,
'pattern': 7
},
{
'control': 0,
'hour': 16,
'id': 2,
'minute': 15,
'pattern': 0
}
]
},
{
'desc': 'null-green-down',
'id': 8,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 8
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '最新测试1-green-up',
'id': 9,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 9
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-green-down',
'id': 10,
'index': null,
'plan': [
{
'control': 10,
'hour': 7,
'id': 1,
'minute': 15,
'pattern': 10
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 1',
'id': 11,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 11
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-协调绿波上行',
'id': 12,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 12
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 2',
'id': 13,
'index': null,
'plan': [
{
'control': 10,
'hour': 12,
'id': 1,
'minute': 0,
'pattern': 13
},
{
'control': 0,
'hour': 14,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 3',
'id': 14,
'index': null,
'plan': [
{
'control': 10,
'hour': 13,
'id': 1,
'minute': 0,
'pattern': 14
},
{
'control': 0,
'hour': 15,
'id': 2,
'minute': 0,
'pattern': 0
}
]
}
]
},
'fixedtimePlan': {
'agentid': null,
'barriers': [],
'cycle': 39,
'offset': 0,
'phase': [
{
'adjusted': false,
'duration': 22,
'flowperhour': 108,
'id': 1,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.5128205128205128
},
{
'adjusted': false,
'duration': 4,
'flowperhour': 17,
'id': 2,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.1282051282051282
},
{
'adjusted': false,
'duration': 6,
'flowperhour': 27,
'id': 3,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.15384615384615386
},
{
'adjusted': false,
'duration': 7,
'flowperhour': 36,
'id': 4,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.20512820512820513
}
],
'rings': [
{
'num': 1,
'sequence': [
1,
2,
3,
4
],
'y': 0
}
]
},
'name': 'cslhsl',
'patternid': 2,
'phaseflows': [
{
'flowperhour': 108,
'icfsoLaneStat': {
'directionstr': null,
'flow': 108.66666666666667,
'hourlyequivalent': 108,
'laneid': 'cslhsl-3-3',
'seconds': 3600,
'stattime': '07:00:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 17,
'icfsoLaneStat': {
'directionstr': null,
'flow': 17,
'hourlyequivalent': 17,
'laneid': 'cslhsl-3-1',
'seconds': 3600,
'stattime': '07:00:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 27,
'icfsoLaneStat': {
'directionstr': null,
'flow': 27,
'hourlyequivalent': 27,
'laneid': 'cslhsl-4-2',
'seconds': 3600,
'stattime': '07:00:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 36,
'icfsoLaneStat': {
'directionstr': null,
'flow': 36.166666666666667,
'hourlyequivalent': 36,
'laneid': 'cslhsl-4-1',
'seconds': 3600,
'stattime': '07:00:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'timeperiodrange': [
'07:00:00',
'08:00:00'
],
'unitid': 'cslhsl'
},
{
'agentid': 'cslhsl',
'daterange': [
'2024-10-01',
'2024-10-09'
],
'days': [
6,
7,
1,
2,
3,
4,
5
],
'feature': {
'dateList': [
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 1',
'id': 11,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 11
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 2',
'id': 13,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 13
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 3',
'id': 14,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 14
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调绿波上行',
'id': 12,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 12
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-green-down',
'id': 10,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 10
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '最新测试1-green-up',
'id': 9,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 9
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': 'null-green-down',
'id': 8,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 8
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1-green-down',
'id': 6,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 6
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-green-up',
'id': 7,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 7
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-1-green-down',
'id': 5,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 5
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2',
'id': 4,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 4
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1',
'id': 3,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 3
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '下行全天',
'id': 2,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 2
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '日期1',
'id': 1,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 1
}
],
'patternList': [
{
'barriers': [],
'cycle': 40,
'desc': '方案1',
'id': 1,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 24
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 4
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 5
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 7
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '下行全天-green-down',
'id': 2,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 1-green-down',
'id': 3,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-down',
'id': 4,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-1-green-down',
'id': 5,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 115,
'desc': '方案 1-green-up',
'id': 6,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 28
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 29
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-up',
'id': 7,
'offset': 24,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': 'null-green-down',
'id': 8,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '最新测试1-green-up',
'id': 9,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-green-down',
'id': 10,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 1',
'id': 11,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调绿波上行',
'id': 12,
'offset': 21,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 2',
'id': 13,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调方案 3',
'id': 14,
'offset': 109,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
}
],
'patternPhaseMap': null,
'phaseList': [
{
'concurrent': [],
'controltype': 0,
'direction': [
1,
5
],
'flashgreen': 3,
'greenpulse': 5,
'id': 1,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
2,
6
],
'flashgreen': 3,
'greenpulse': 5,
'id': 2,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
9,
13
],
'flashgreen': 3,
'greenpulse': 5,
'id': 3,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
10,
14
],
'flashgreen': 3,
'greenpulse': 5,
'id': 4,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
}
],
'phases': [
{
'flowperhour': 94,
'icfsoLaneStat': {
'directionstr': null,
'flow': 283.75,
'hourlyequivalent': 94,
'laneid': 'cslhsl-3-3',
'seconds': 10800,
'stattime': '08:00:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 14,
'icfsoLaneStat': {
'directionstr': null,
'flow': 43.75,
'hourlyequivalent': 14,
'laneid': 'cslhsl-3-1',
'seconds': 10800,
'stattime': '08:00:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 18,
'icfsoLaneStat': {
'directionstr': null,
'flow': 57.375,
'hourlyequivalent': 18,
'laneid': 'cslhsl-4-2',
'seconds': 10800,
'stattime': '08:00:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 29,
'icfsoLaneStat': {
'directionstr': null,
'flow': 89.875,
'hourlyequivalent': 29,
'laneid': 'cslhsl-4-1',
'seconds': 10800,
'stattime': '08:00:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'planList': [
{
'desc': '计划1',
'id': 1,
'index': null,
'plan': [
{
'control': 5,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 1
},
{
'control': 5,
'hour': 0,
'id': 2,
'minute': 4,
'pattern': 1
}
]
},
{
'desc': '下行全天',
'id': 2,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 2
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 55,
'pattern': 0
}
]
},
{
'desc': '方案 1',
'id': 3,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2',
'id': 4,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 4
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2-1-green-down',
'id': 5,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 5
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 1-green-down',
'id': 6,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '方案 2-green-up',
'id': 7,
'index': null,
'plan': [
{
'control': 10,
'hour': 10,
'id': 1,
'minute': 0,
'pattern': 7
},
{
'control': 0,
'hour': 16,
'id': 2,
'minute': 15,
'pattern': 0
}
]
},
{
'desc': 'null-green-down',
'id': 8,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 8
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '最新测试1-green-up',
'id': 9,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 9
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-green-down',
'id': 10,
'index': null,
'plan': [
{
'control': 10,
'hour': 7,
'id': 1,
'minute': 15,
'pattern': 10
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 1',
'id': 11,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 11
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-协调绿波上行',
'id': 12,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 12
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 2',
'id': 13,
'index': null,
'plan': [
{
'control': 10,
'hour': 12,
'id': 1,
'minute': 0,
'pattern': 13
},
{
'control': 0,
'hour': 14,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 3',
'id': 14,
'index': null,
'plan': [
{
'control': 10,
'hour': 13,
'id': 1,
'minute': 0,
'pattern': 14
},
{
'control': 0,
'hour': 15,
'id': 2,
'minute': 0,
'pattern': 0
}
]
}
]
},
'fixedtimePlan': {
'agentid': null,
'barriers': [],
'cycle': 40,
'offset': 0,
'phase': [
{
'adjusted': false,
'duration': 24,
'flowperhour': 94,
'id': 1,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.5641025641025641
},
{
'adjusted': false,
'duration': 4,
'flowperhour': 14,
'id': 2,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.10256410256410256
},
{
'adjusted': false,
'duration': 5,
'flowperhour': 18,
'id': 3,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.15384615384615386
},
{
'adjusted': false,
'duration': 7,
'flowperhour': 29,
'id': 4,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.1794871794871795
}
],
'rings': [
{
'num': 1,
'sequence': [
1,
2,
3,
4
],
'y': 0
}
]
},
'name': 'cslhsl',
'patternid': 3,
'phaseflows': [
{
'flowperhour': 94,
'icfsoLaneStat': {
'directionstr': null,
'flow': 283.75,
'hourlyequivalent': 94,
'laneid': 'cslhsl-3-3',
'seconds': 10800,
'stattime': '08:00:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 14,
'icfsoLaneStat': {
'directionstr': null,
'flow': 43.75,
'hourlyequivalent': 14,
'laneid': 'cslhsl-3-1',
'seconds': 10800,
'stattime': '08:00:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 18,
'icfsoLaneStat': {
'directionstr': null,
'flow': 57.375,
'hourlyequivalent': 18,
'laneid': 'cslhsl-4-2',
'seconds': 10800,
'stattime': '08:00:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 29,
'icfsoLaneStat': {
'directionstr': null,
'flow': 89.875,
'hourlyequivalent': 29,
'laneid': 'cslhsl-4-1',
'seconds': 10800,
'stattime': '08:00:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'timeperiodrange': [
'08:00:00',
'11:00:00'
],
'unitid': 'cslhsl'
},
{
'agentid': 'cslhsl',
'daterange': [
'2024-10-01',
'2024-10-09'
],
'days': [
6,
7,
1,
2,
3,
4,
5
],
'feature': {
'dateList': [
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 1',
'id': 11,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 11
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 2',
'id': 13,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 13
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 3',
'id': 14,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 14
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调绿波上行',
'id': 12,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 12
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-green-down',
'id': 10,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 10
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '最新测试1-green-up',
'id': 9,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 9
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': 'null-green-down',
'id': 8,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 8
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1-green-down',
'id': 6,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 6
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-green-up',
'id': 7,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 7
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-1-green-down',
'id': 5,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 5
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2',
'id': 4,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 4
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1',
'id': 3,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 3
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '下行全天',
'id': 2,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 2
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '日期1',
'id': 1,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 1
}
],
'patternList': [
{
'barriers': [],
'cycle': 41,
'desc': '方案1',
'id': 1,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 24
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 4
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 5
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 8
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '下行全天-green-down',
'id': 2,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 1-green-down',
'id': 3,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-down',
'id': 4,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-1-green-down',
'id': 5,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 115,
'desc': '方案 1-green-up',
'id': 6,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 28
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 29
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-up',
'id': 7,
'offset': 24,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': 'null-green-down',
'id': 8,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '最新测试1-green-up',
'id': 9,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-green-down',
'id': 10,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 1',
'id': 11,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调绿波上行',
'id': 12,
'offset': 21,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 2',
'id': 13,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调方案 3',
'id': 14,
'offset': 109,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
}
],
'patternPhaseMap': null,
'phaseList': [
{
'concurrent': [],
'controltype': 0,
'direction': [
1,
5
],
'flashgreen': 3,
'greenpulse': 5,
'id': 1,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
2,
6
],
'flashgreen': 3,
'greenpulse': 5,
'id': 2,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
9,
13
],
'flashgreen': 3,
'greenpulse': 5,
'id': 3,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
10,
14
],
'flashgreen': 3,
'greenpulse': 5,
'id': 4,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
}
],
'phases': [
{
'flowperhour': 125,
'icfsoLaneStat': {
'directionstr': null,
'flow': 189,
'hourlyequivalent': 125,
'laneid': 'cslhsl-3-3',
'seconds': 5400,
'stattime': '11:00:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 20,
'icfsoLaneStat': {
'directionstr': null,
'flow': 31,
'hourlyequivalent': 20,
'laneid': 'cslhsl-3-1',
'seconds': 5400,
'stattime': '11:00:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 25,
'icfsoLaneStat': {
'directionstr': null,
'flow': 38,
'hourlyequivalent': 25,
'laneid': 'cslhsl-4-2',
'seconds': 5400,
'stattime': '11:00:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 40,
'icfsoLaneStat': {
'directionstr': null,
'flow': 61.57142857142857,
'hourlyequivalent': 40,
'laneid': 'cslhsl-4-1',
'seconds': 5400,
'stattime': '11:00:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'planList': [
{
'desc': '计划1',
'id': 1,
'index': null,
'plan': [
{
'control': 5,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 1
},
{
'control': 5,
'hour': 0,
'id': 2,
'minute': 4,
'pattern': 1
}
]
},
{
'desc': '下行全天',
'id': 2,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 2
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 55,
'pattern': 0
}
]
},
{
'desc': '方案 1',
'id': 3,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2',
'id': 4,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 4
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2-1-green-down',
'id': 5,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 5
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 1-green-down',
'id': 6,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '方案 2-green-up',
'id': 7,
'index': null,
'plan': [
{
'control': 10,
'hour': 10,
'id': 1,
'minute': 0,
'pattern': 7
},
{
'control': 0,
'hour': 16,
'id': 2,
'minute': 15,
'pattern': 0
}
]
},
{
'desc': 'null-green-down',
'id': 8,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 8
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '最新测试1-green-up',
'id': 9,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 9
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-green-down',
'id': 10,
'index': null,
'plan': [
{
'control': 10,
'hour': 7,
'id': 1,
'minute': 15,
'pattern': 10
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 1',
'id': 11,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 11
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-协调绿波上行',
'id': 12,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 12
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 2',
'id': 13,
'index': null,
'plan': [
{
'control': 10,
'hour': 12,
'id': 1,
'minute': 0,
'pattern': 13
},
{
'control': 0,
'hour': 14,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 3',
'id': 14,
'index': null,
'plan': [
{
'control': 10,
'hour': 13,
'id': 1,
'minute': 0,
'pattern': 14
},
{
'control': 0,
'hour': 15,
'id': 2,
'minute': 0,
'pattern': 0
}
]
}
]
},
'fixedtimePlan': {
'agentid': null,
'barriers': [],
'cycle': 41,
'offset': 0,
'phase': [
{
'adjusted': false,
'duration': 24,
'flowperhour': 125,
'id': 1,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.6
},
{
'adjusted': false,
'duration': 4,
'flowperhour': 20,
'id': 2,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.1
},
{
'adjusted': false,
'duration': 5,
'flowperhour': 25,
'id': 3,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.125
},
{
'adjusted': false,
'duration': 8,
'flowperhour': 40,
'id': 4,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.175
}
],
'rings': [
{
'num': 1,
'sequence': [
1,
2,
3,
4
],
'y': 0
}
]
},
'name': 'cslhsl',
'patternid': 4,
'phaseflows': [
{
'flowperhour': 125,
'icfsoLaneStat': {
'directionstr': null,
'flow': 189,
'hourlyequivalent': 125,
'laneid': 'cslhsl-3-3',
'seconds': 5400,
'stattime': '11:00:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 20,
'icfsoLaneStat': {
'directionstr': null,
'flow': 31,
'hourlyequivalent': 20,
'laneid': 'cslhsl-3-1',
'seconds': 5400,
'stattime': '11:00:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 25,
'icfsoLaneStat': {
'directionstr': null,
'flow': 38,
'hourlyequivalent': 25,
'laneid': 'cslhsl-4-2',
'seconds': 5400,
'stattime': '11:00:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 40,
'icfsoLaneStat': {
'directionstr': null,
'flow': 61.57142857142857,
'hourlyequivalent': 40,
'laneid': 'cslhsl-4-1',
'seconds': 5400,
'stattime': '11:00:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'timeperiodrange': [
'11:00:00',
'12:30:00'
],
'unitid': 'cslhsl'
},
{
'agentid': 'cslhsl',
'daterange': [
'2024-10-01',
'2024-10-09'
],
'days': [
6,
7,
1,
2,
3,
4,
5
],
'feature': {
'dateList': [
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 1',
'id': 11,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 11
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 2',
'id': 13,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 13
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 3',
'id': 14,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 14
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调绿波上行',
'id': 12,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 12
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-green-down',
'id': 10,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 10
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '最新测试1-green-up',
'id': 9,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 9
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': 'null-green-down',
'id': 8,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 8
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1-green-down',
'id': 6,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 6
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-green-up',
'id': 7,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 7
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-1-green-down',
'id': 5,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 5
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2',
'id': 4,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 4
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1',
'id': 3,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 3
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '下行全天',
'id': 2,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 2
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '日期1',
'id': 1,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 1
}
],
'patternList': [
{
'barriers': [],
'cycle': 40,
'desc': '方案1',
'id': 1,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 27
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 3
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 4
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 6
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '下行全天-green-down',
'id': 2,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 1-green-down',
'id': 3,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-down',
'id': 4,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-1-green-down',
'id': 5,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 115,
'desc': '方案 1-green-up',
'id': 6,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 28
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 29
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-up',
'id': 7,
'offset': 24,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': 'null-green-down',
'id': 8,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '最新测试1-green-up',
'id': 9,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-green-down',
'id': 10,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 1',
'id': 11,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调绿波上行',
'id': 12,
'offset': 21,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 2',
'id': 13,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调方案 3',
'id': 14,
'offset': 109,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
}
],
'patternPhaseMap': null,
'phaseList': [
{
'concurrent': [],
'controltype': 0,
'direction': [
1,
5
],
'flashgreen': 3,
'greenpulse': 5,
'id': 1,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
2,
6
],
'flashgreen': 3,
'greenpulse': 5,
'id': 2,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
9,
13
],
'flashgreen': 3,
'greenpulse': 5,
'id': 3,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
10,
14
],
'flashgreen': 3,
'greenpulse': 5,
'id': 4,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
}
],
'phases': [
{
'flowperhour': 134,
'icfsoLaneStat': {
'directionstr': null,
'flow': 673.5714285714286,
'hourlyequivalent': 134,
'laneid': 'cslhsl-1-2',
'seconds': 18000,
'stattime': '12:30:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 15,
'icfsoLaneStat': {
'directionstr': null,
'flow': 80,
'hourlyequivalent': 15,
'laneid': 'cslhsl-3-1',
'seconds': 18000,
'stattime': '12:30:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 20,
'icfsoLaneStat': {
'directionstr': null,
'flow': 105.42857142857143,
'hourlyequivalent': 20,
'laneid': 'cslhsl-4-2',
'seconds': 18000,
'stattime': '12:30:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 32,
'icfsoLaneStat': {
'directionstr': null,
'flow': 165.57142857142859,
'hourlyequivalent': 32,
'laneid': 'cslhsl-4-1',
'seconds': 18000,
'stattime': '12:30:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'planList': [
{
'desc': '计划1',
'id': 1,
'index': null,
'plan': [
{
'control': 5,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 1
},
{
'control': 5,
'hour': 0,
'id': 2,
'minute': 4,
'pattern': 1
}
]
},
{
'desc': '下行全天',
'id': 2,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 2
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 55,
'pattern': 0
}
]
},
{
'desc': '方案 1',
'id': 3,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2',
'id': 4,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 4
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2-1-green-down',
'id': 5,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 5
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 1-green-down',
'id': 6,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '方案 2-green-up',
'id': 7,
'index': null,
'plan': [
{
'control': 10,
'hour': 10,
'id': 1,
'minute': 0,
'pattern': 7
},
{
'control': 0,
'hour': 16,
'id': 2,
'minute': 15,
'pattern': 0
}
]
},
{
'desc': 'null-green-down',
'id': 8,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 8
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '最新测试1-green-up',
'id': 9,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 9
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-green-down',
'id': 10,
'index': null,
'plan': [
{
'control': 10,
'hour': 7,
'id': 1,
'minute': 15,
'pattern': 10
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 1',
'id': 11,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 11
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-协调绿波上行',
'id': 12,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 12
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 2',
'id': 13,
'index': null,
'plan': [
{
'control': 10,
'hour': 12,
'id': 1,
'minute': 0,
'pattern': 13
},
{
'control': 0,
'hour': 14,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 3',
'id': 14,
'index': null,
'plan': [
{
'control': 10,
'hour': 13,
'id': 1,
'minute': 0,
'pattern': 14
},
{
'control': 0,
'hour': 15,
'id': 2,
'minute': 0,
'pattern': 0
}
]
}
]
},
'fixedtimePlan': {
'agentid': null,
'barriers': [],
'cycle': 40,
'offset': 0,
'phase': [
{
'adjusted': false,
'duration': 27,
'flowperhour': 134,
'id': 1,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.5853658536585366
},
{
'adjusted': false,
'duration': 3,
'flowperhour': 15,
'id': 2,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.0975609756097561
},
{
'adjusted': false,
'duration': 4,
'flowperhour': 20,
'id': 3,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.12195121951219512
},
{
'adjusted': false,
'duration': 6,
'flowperhour': 32,
'id': 4,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.1951219512195122
}
],
'rings': [
{
'num': 1,
'sequence': [
1,
2,
3,
4
],
'y': 0
}
]
},
'name': 'cslhsl',
'patternid': 5,
'phaseflows': [
{
'flowperhour': 134,
'icfsoLaneStat': {
'directionstr': null,
'flow': 673.5714285714286,
'hourlyequivalent': 134,
'laneid': 'cslhsl-1-2',
'seconds': 18000,
'stattime': '12:30:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 15,
'icfsoLaneStat': {
'directionstr': null,
'flow': 80,
'hourlyequivalent': 15,
'laneid': 'cslhsl-3-1',
'seconds': 18000,
'stattime': '12:30:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 20,
'icfsoLaneStat': {
'directionstr': null,
'flow': 105.42857142857143,
'hourlyequivalent': 20,
'laneid': 'cslhsl-4-2',
'seconds': 18000,
'stattime': '12:30:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 32,
'icfsoLaneStat': {
'directionstr': null,
'flow': 165.57142857142859,
'hourlyequivalent': 32,
'laneid': 'cslhsl-4-1',
'seconds': 18000,
'stattime': '12:30:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'timeperiodrange': [
'12:30:00',
'17:30:00'
],
'unitid': 'cslhsl'
},
{
'agentid': 'cslhsl',
'daterange': [
'2024-10-01',
'2024-10-09'
],
'days': [
6,
7,
1,
2,
3,
4,
5
],
'feature': {
'dateList': [
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 1',
'id': 11,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 11
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 2',
'id': 13,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 13
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调方案 3',
'id': 14,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 14
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-协调绿波上行',
'id': 12,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 12
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '成山路-green-down',
'id': 10,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 10
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '最新测试1-green-up',
'id': 9,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 9
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': 'null-green-down',
'id': 8,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 8
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1-green-down',
'id': 6,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 6
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-green-up',
'id': 7,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 7
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2-1-green-down',
'id': 5,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 5
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 2',
'id': 4,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 4
},
{
'date': [],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '方案 1',
'id': 3,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 3
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '下行全天',
'id': 2,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 2
},
{
'date': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31
],
'day': [
0,
1,
2,
3,
4,
5,
6
],
'desc': '日期1',
'id': 1,
'month': [
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12
],
'plan': 1
}
],
'patternList': [
{
'barriers': [],
'cycle': 38,
'desc': '方案1',
'id': 1,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 21
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 4
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 5
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 8
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '下行全天-green-down',
'id': 2,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 1-green-down',
'id': 3,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-down',
'id': 4,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-1-green-down',
'id': 5,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 115,
'desc': '方案 1-green-up',
'id': 6,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 28
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 29
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 29
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '方案 2-green-up',
'id': 7,
'offset': 24,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': 'null-green-down',
'id': 8,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '最新测试1-green-up',
'id': 9,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-green-down',
'id': 10,
'offset': 106,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 1',
'id': 11,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调绿波上行',
'id': 12,
'offset': 21,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 57
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 20
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 20
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 120,
'desc': '成山路-协调方案 2',
'id': 13,
'offset': 0,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 30
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 30
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
},
{
'barriers': [],
'cycle': 130,
'desc': '成山路-协调方案 3',
'id': 14,
'offset': 109,
'overlapList': null,
'rings': [
[
{
'id': 1,
'mode': 2,
'name': null,
'options': null,
'value': 31
},
{
'id': 2,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 3,
'mode': 2,
'name': null,
'options': null,
'value': 33
},
{
'id': 4,
'mode': 2,
'name': null,
'options': null,
'value': 33
}
],
[],
[],
[]
],
'stages': [
[
1
],
[
2
],
[
3
],
[
4
]
],
'stagesList': [
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
},
{
'key': 0,
'phases': null,
'split': 30
}
]
}
],
'patternPhaseMap': null,
'phaseList': [
{
'concurrent': [],
'controltype': 0,
'direction': [
1,
5
],
'flashgreen': 3,
'greenpulse': 5,
'id': 1,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
2,
6
],
'flashgreen': 3,
'greenpulse': 5,
'id': 2,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
9,
13
],
'flashgreen': 3,
'greenpulse': 5,
'id': 3,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
},
{
'concurrent': [],
'controltype': 0,
'direction': [
10,
14
],
'flashgreen': 3,
'greenpulse': 5,
'id': 4,
'max1': 120,
'max2': 180,
'mingreen': 6,
'passage': 3,
'pedclear': 0,
'peddirection': [],
'phasewalk': 0,
'redclear': 2,
'redpulse': 10,
'redyellow': 0,
'ring': 1,
'yellow': 3
}
],
'phases': [
{
'flowperhour': 69,
'icfsoLaneStat': {
'directionstr': null,
'flow': 139.71428571428573,
'hourlyequivalent': 69,
'laneid': 'cslhsl-1-2',
'seconds': 7200,
'stattime': '17:30:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 12,
'icfsoLaneStat': {
'directionstr': null,
'flow': 25.714285714285717,
'hourlyequivalent': 12,
'laneid': 'cslhsl-3-1',
'seconds': 7200,
'stattime': '17:30:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 18,
'icfsoLaneStat': {
'directionstr': null,
'flow': 37.285714285714288,
'hourlyequivalent': 18,
'laneid': 'cslhsl-4-2',
'seconds': 7200,
'stattime': '17:30:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 27,
'icfsoLaneStat': {
'directionstr': null,
'flow': 55.857142857142857,
'hourlyequivalent': 27,
'laneid': 'cslhsl-4-1',
'seconds': 7200,
'stattime': '17:30:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'planList': [
{
'desc': '计划1',
'id': 1,
'index': null,
'plan': [
{
'control': 5,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 1
},
{
'control': 5,
'hour': 0,
'id': 2,
'minute': 4,
'pattern': 1
}
]
},
{
'desc': '下行全天',
'id': 2,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 2
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 55,
'pattern': 0
}
]
},
{
'desc': '方案 1',
'id': 3,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2',
'id': 4,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 4
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 2-1-green-down',
'id': 5,
'index': null,
'plan': [
{
'control': 10,
'hour': 0,
'id': 1,
'minute': 0,
'pattern': 5
},
{
'control': 0,
'hour': 23,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '方案 1-green-down',
'id': 6,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 3
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '方案 2-green-up',
'id': 7,
'index': null,
'plan': [
{
'control': 10,
'hour': 10,
'id': 1,
'minute': 0,
'pattern': 7
},
{
'control': 0,
'hour': 16,
'id': 2,
'minute': 15,
'pattern': 0
}
]
},
{
'desc': 'null-green-down',
'id': 8,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 8
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '最新测试1-green-up',
'id': 9,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 9
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-green-down',
'id': 10,
'index': null,
'plan': [
{
'control': 10,
'hour': 7,
'id': 1,
'minute': 15,
'pattern': 10
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 45,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 1',
'id': 11,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 11
},
{
'control': 0,
'hour': 9,
'id': 2,
'minute': 30,
'pattern': 0
}
]
},
{
'desc': '成山路-协调绿波上行',
'id': 12,
'index': null,
'plan': [
{
'control': 10,
'hour': 8,
'id': 1,
'minute': 0,
'pattern': 12
},
{
'control': 0,
'hour': 20,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 2',
'id': 13,
'index': null,
'plan': [
{
'control': 10,
'hour': 12,
'id': 1,
'minute': 0,
'pattern': 13
},
{
'control': 0,
'hour': 14,
'id': 2,
'minute': 0,
'pattern': 0
}
]
},
{
'desc': '成山路-协调方案 3',
'id': 14,
'index': null,
'plan': [
{
'control': 10,
'hour': 13,
'id': 1,
'minute': 0,
'pattern': 14
},
{
'control': 0,
'hour': 15,
'id': 2,
'minute': 0,
'pattern': 0
}
]
}
]
},
'fixedtimePlan': {
'agentid': null,
'barriers': [],
'cycle': 38,
'offset': 0,
'phase': [
{
'adjusted': false,
'duration': 21,
'flowperhour': 69,
'id': 1,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.675
},
{
'adjusted': false,
'duration': 4,
'flowperhour': 12,
'id': 2,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.075
},
{
'adjusted': false,
'duration': 5,
'flowperhour': 18,
'id': 3,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.1
},
{
'adjusted': false,
'duration': 8,
'flowperhour': 27,
'id': 4,
'maxgreen': 120,
'mingreen': 6,
'saturation': 1700,
'splitratio': 0.15
}
],
'rings': [
{
'num': 1,
'sequence': [
1,
2,
3,
4
],
'y': 0
}
]
},
'name': 'cslhsl',
'patternid': 6,
'phaseflows': [
{
'flowperhour': 69,
'icfsoLaneStat': {
'directionstr': null,
'flow': 139.71428571428573,
'hourlyequivalent': 69,
'laneid': 'cslhsl-1-2',
'seconds': 7200,
'stattime': '17:30:00.000000001+08:00'
},
'id': 1,
'saturation': 1700
},
{
'flowperhour': 12,
'icfsoLaneStat': {
'directionstr': null,
'flow': 25.714285714285717,
'hourlyequivalent': 12,
'laneid': 'cslhsl-3-1',
'seconds': 7200,
'stattime': '17:30:00.000000001+08:00'
},
'id': 2,
'saturation': 1700
},
{
'flowperhour': 18,
'icfsoLaneStat': {
'directionstr': null,
'flow': 37.285714285714288,
'hourlyequivalent': 18,
'laneid': 'cslhsl-4-2',
'seconds': 7200,
'stattime': '17:30:00.000000001+08:00'
},
'id': 3,
'saturation': 1700
},
{
'flowperhour': 27,
'icfsoLaneStat': {
'directionstr': null,
'flow': 55.857142857142857,
'hourlyequivalent': 27,
'laneid': 'cslhsl-4-1',
'seconds': 7200,
'stattime': '17:30:00.000000001+08:00'
},
'id': 4,
'saturation': 1700
}
],
'timeperiodrange': [
'17:30:00',
'19:30:00'
],
'unitid': 'cslhsl'
}
]
}
]
      }
    }
  },
  methods: {
    getSaveParam () {
      console.log('getPatternConfig')
      let res = this.$refs.patternConfig.getPatternConfig()
      console.log(res)
      console.log(JSON.stringify(res))
      return res
    },
    setPropsToken (token) {
      // 获取组件外传入的token，便于独立组件调用接口
      if (token && token !== '') {
        setToken(token)
      }
    }
  },
  created () {
    // this.setDialogWidth()
  },
  mounted () {
    // let _this = this
    // setTimeout(function () {
    //   alert('Hello')
    //   // _this.agentId = 'scats1'
    //   _this.agentids = ['gjnlahql', 'gjnlgjxl']
    // }, 5 * 1000)
    this.setPropsToken(this.Token135)
    // this.setPropsToken(this.Token)
    window.onresize = () => {
      return (() => {
        // this.setDialogWidth()
      })()
    }
  },
  destroyed () {
  }
}
</script>
<style lang='scss'>
.layout-container {
  width: 800PX;
}
.layout-container1 {
  width: 242PX;
  height: 176PX;
  border: 1PX solid green;
}
.layout-container2 {
  /* height: 600px; */
  width: 1020px;
  // height: 620px;
  height: 470px;
  border: 1PX solid yellow;
  position: relative;
}
.abow_dialog {
    display: flex;
    justify-content: center;
    align-items: Center;
    overflow: hidden;
    .el-dialog {
        margin: 0 auto !important;
        height: 90%;
        overflow: hidden;
        .el-dialog__body {
            position: absolute;
            left: 0;
            top: 54px;
            bottom: 0;
            right: 0;
            padding: 0;
            z-index: 1;
            overflow-y: auto;
            overflow-x: auto;
        }
    }
}
</style>
