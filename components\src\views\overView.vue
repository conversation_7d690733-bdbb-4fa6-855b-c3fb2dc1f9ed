<template>
  <div id="test1">
    <!-- <overview
        :AgentId="agentId"
        :reqUrl="reqUrl"
        :modeName="modeName"
        :controlName="controlName"
        :isShowState="isShowState"
        :isShowMode="isShowMode"
        :Token="Token"></overview> -->
    <el-button @click="handleChangeOverview">切换overview组件</el-button>
    <overview-component
        :AgentId="agentId"
        :isShowInterval="isShowInterval"
        :isShowMessage ="isShowMessage"/>
  </div>
</template>

<script>
export default {
  data () {
    return {
      isShowState: true,
      isShowMode: true,
      modeName: '交警遥控',
      controlName: '步进',
      agentId: '12008_835_ticp',
      agentid1: 'gjnljml',
      agentid2: '12008_835_ticp',
      isShowMessage: false,
      isShowInterval: true,
      sum: 0,
      Token: 'eyJraWQiOiIxNzMzODg3MDYyMjgwIiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTczMzg5MDY2MiwiaWF0IjoxNzMzODgzNDYyfQ.qUJZ4lKv0WU3s3lWfYwl5ZM77n2rriYeFQ8qOwRFuzk',
      reqUrl: 'http://**************:10003/openatc',
      isOnlyMap: true
    }
  },
  methods: {
    handleChangeOverview () {
      if (this.sum % 2 === 0) {
        this.agentId = this.agentid1
      } else {
        this.agentId = this.agentid2
      }
      this.sum++
    },
    reverseOverviewBtMap () {
      this.isOnlyMap = !this.isOnlyMap
    }
  },
  watch: {
  },
  mounted () {
  }
}
</script>
<style scoped>
.wrap {
  /* background: #333333; */
  width: 100%;
  height: 100%;
}
</style>
