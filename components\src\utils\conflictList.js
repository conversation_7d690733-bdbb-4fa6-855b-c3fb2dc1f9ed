import { computedRelation } from './conflct.js'
export default class CrossDirectionConflictList {
  constructor (agentid, choosedDirection, choosedPedDirection) {
    this.agentid = agentid
    this.conflictList = []
    this.phaseConflictMap = new Map()
    this.pedPhaseConflictMap = new Map()
    this.allConflictDir = []
    this.allPedConflictDir = []
    this.choosedDirection = choosedDirection
    this.choosedPedDirection = choosedPedDirection
  }

  getConflictListByAgentid () {
    return new Promise((resolve, reject) => {
      computedRelation(this.agentid).then(res => {
        if (!res) return
        this.conflictList = res
        this.setAllConflictMap()
        resolve(res)
      })
    })
  }

  setAllConflictMap () {
    for (let i = 0; i < this.conflictList.length; i++) {
      let item = this.conflictList[i]
      if (item.type === 'phase') {
        this.phaseConflictMap.set(item.direction, item)
      }
      if (item.type === 'pedphase') {
        this.pedPhaseConflictMap.set(item.direction, item)
      }
    }
    // console.log(this.phaseConflictMap)
  }

  getOneDirConflict (dir, type) {
    let conflictDir = []
    let conflictPedDir = []
    if (type === 'phase') {
      let phaseInfo = this.phaseConflictMap.get(dir)
      if (!phaseInfo) return
      conflictDir = phaseInfo.laneConflictList
      conflictPedDir = phaseInfo.pedConflictList
    }
    if (type === 'pedphase') {
      let pedPhaseInfo = this.pedPhaseConflictMap.get(dir)
      if (!pedPhaseInfo) return
      conflictDir = pedPhaseInfo.laneConflictList
      conflictPedDir = pedPhaseInfo.pedConflictList
    }
    return {
      conflictDir,
      conflictPedDir
    }
  }

  getListDirConflict (choosedDirection, choosedPedDirection) {
    for (let i = 0; i < choosedDirection.length; i++) {
      let phaseConflict = this.getOneDirConflict(choosedDirection[i], 'phase')
      if (phaseConflict) {
        let conflictDirArr = phaseConflict.conflictDir
        let conflictPedDirArr = phaseConflict.conflictPedDir
        this.allConflictDir = this.allConflictDir.concat(conflictDirArr)
        this.allPedConflictDir = this.allPedConflictDir.concat(conflictPedDirArr)
      }
    }
    for (let i = 0; i < choosedPedDirection.length; i++) {
      let pedPhaseConflict = this.getOneDirConflict(choosedPedDirection[i], 'pedphase')
      if (pedPhaseConflict) {
        let conflictDirArr = pedPhaseConflict.conflictDir
        let conflictPedDirArr = pedPhaseConflict.conflictPedDir
        this.allConflictDir = this.allConflictDir.concat(conflictDirArr)
        this.allPedConflictDir = this.allPedConflictDir.concat(conflictPedDirArr)
      }
    }
    this.allConflictDir = Array.from(new Set(this.allConflictDir))
    this.allPedConflictDir = Array.from(new Set(this.allPedConflictDir))
    // console.log(this.allConflictDir)
    // console.log(this.allPedConflictDir)
    return {
      allConflictDir: this.allConflictDir,
      allPedConflictDir: this.allPedConflictDir
    }
  }
}
