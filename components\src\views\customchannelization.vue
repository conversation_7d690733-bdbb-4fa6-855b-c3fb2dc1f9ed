<template>
<!-- 渠化绘制demo -->
  <div style="height: 100%;">
      <custom-channelization
        :AgentId="agentId" />
      <!-- <CustomChannelization
        :AgentId="agentId"
        :tscParam="tscParams"
        @saveCallback="saveCallback" /> -->
  </div>
</template>
<script>
import { getIntersectionInfo } from '../api/template.js'
import CustomChannelization from '../kisscomps/components/DrawChannelization/drawsvg/index'
export default {
  name: 'custom-channelization-demo',
  data () {
    return {
      agentId: '13000000000000000003',
      tscParams: {}
    }
  },
  components: {
    CustomChannelization
  },
  methods: {
    getIntersectionInfo () {
      // 获取路口信息
      getIntersectionInfo(this.agentId).then(res => {
        console.log('接口上载的参数', res.data.data.param)
        this.tscParams = res.data.data.param
      })
    },
    saveCallback (res) {
      // 保存渠化图回调
      console.log('保存接口返回的参数', res)
    }
  },
  created () {
  },
  mounted () {
    // this.getIntersectionInfo()
  },
  destroyed () {
  }
}
</script>
<style lang="scss">
</style>
