/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use i18n software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
import i18n from '../i18n/index.js'
export default class ControlFormat {
  constructor (crossStatusData, phaseList) {
    this.ParamsMode = new Map([[0, '自主控制'], [1, '本地手动'], [2, '平台控制'], [3, '配置软件控制'], [4, '遥控器控制'], [5, '黄闪器控制'], [6, '降级控制'], [7, '脚本控制'], [8, '算法控制']])
    this.ParamsModeEn = new Map([[0, 'Autonomous Control'], [1, 'Local Manual'], [2, 'Platform Control'], [3, 'Configuration Software Control'], [4, 'Remote Control'], [5, 'Yellow Flasher Control'], [6, 'Degradation Control'], [7, 'Script Control'], [8, 'Algorithm Control']])
    this.ParamsControl = new Map([[0, '多时段'], [1, '黄闪'], [2, '全红'], [3, '关灯'], [4, '步进'], [5, '定周期控制'], [6, '自适应'], [7, '协调感应控制'], [8, '方案选择控制'], [9, '方案生成'], [10, '无电缆控制'], [11, '有电缆控制'], [12, '行人过街控制'], [13, '方案恢复过渡'], [14, '相位驻留'], [15, '通道检测'], [16, '方向锁定'], [17, '韦伯斯特单点控制'], [18, '预留18'], [19, '感应式行人过街'], [20, '方案干预'], [100, '方案干预'], [21, '预留21'], [22, '相位锁定'], [23, '相位放行控制'], [24, '紧急控制'], [99, '设备维护']])
    this.ParamsControlEn = new Map([[0, 'Multi Period'], [1, 'Yellow Flash Control'], [2, 'Red Control'], [3, 'Dark Control'], [4, 'Step'], [5, 'Fixed_Cycle Control'], [6, 'Free Control'], [7, 'Coordinated Induction Control'], [8, 'Pattern Selection Control'], [9, 'Adaptive Control'], [10, 'Wireless Control'], [11, 'Cable Control'], [12, 'Pedestrian Crossing Control'], [13, 'Pattern recovery'], [14, 'Phase dwell'], [15, 'Channel Detection'], [16, 'Direction lock'], [17, 'Webster Single Point Coordination'], [18, 'Reserving18'], [19, 'Inductive Pedestrian Crossing Control'], [20, 'Program Intervention'], [100, 'Program Intervention'], [21, 'Reserving21'], [22, 'Priority Control'], [23, 'Close Phase'], [24, 'Emergency Control'], [99, 'Device Mantenance']])
    this.phaseType = new Map([[1, '红'], [2, '黄'], [3, '绿']]) // phaseType表示红，黄，绿
    this.phaseTypeEn = new Map([[1, 'Red'], [2, 'Yellow'], [3, 'Green']]) // phaseType表示红，黄，绿
  }
  handleGetData (data) {
    let that = this
    if (data.name === '') {
      if (i18n.locale === 'en') {
        data.name = 'Pattern' + data.patternid
      } else if (i18n.locale === 'zh') {
        data.name = '方案' + data.patternid
      }
    }
    Object.keys(data).forEach(function (key) {
      if (i18n.locale === 'en') {
        if (key === 'mode') {
          if (that.ParamsModeEn.get(data[key]) !== undefined) {
            data[key] = that.ParamsModeEn.get(data[key])
          } else {
            data[key] = that.ParamsModeEn.get(0)
          }
        }
        if (key === 'control') {
          data[key] = that.ParamsControlEn.get(data[key])
        }
        if (key === 'phase') {
          for (let val of data[key]) {
            val.type = that.phaseTypeEn.get(val.type)
          }
        }
      } else if (i18n.locale === 'zh') {
        if (key === 'mode') {
          if (that.ParamsMode.get(data[key]) !== undefined) {
            data[key] = that.ParamsMode.get(data[key])
          } else {
            data[key] = that.ParamsMode.get(0)
          }
        }
        if (key === 'control') {
          data[key] = that.ParamsControl.get(data[key])
        }
        if (key === 'phase') {
          for (let val of data[key]) {
            val.type = that.phaseType.get(val.type)
          }
        }
      }
    })
    return data
  }
}
