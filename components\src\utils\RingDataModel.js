/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use i18n software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
import i18n from '../i18n/index.js'
import PhaseDataModel from '../kisscomps/components/IntersectionMap/crossDirection/utils.js'
import CrossDiagramMgr from '../EdgeMgr/controller/crossDiagramMgr.js'
export default class RingDataModel {
  constructor (crossStatusData = {}, phaseList, busPhaseData = []) {
    this.crossStatusData = crossStatusData
    this.phaseList = phaseList
    this.busPhaseData = busPhaseData
    this.PhaseDataModel = new PhaseDataModel()
    this.CrossDiagramMgr = new CrossDiagramMgr()
  }

  initRingPhaseData () {
    // 环信息从单独上载相位信息里获取，以免相位锁定后，方案状态数据里没有rings，导致相位锁定控制列表无法显示
    let phaseRings = []
    let map = {}
    let dest = []
    for (let i = 0; i < this.phaseList.length; i++) {
      let ai = this.phaseList[i]
      if (!map[ai.ring]) {
        let addphse = this.addPhaseInfo(ai)
        dest.push({
          num: ai.ring,
          phases: [{...ai, ...addphse}]
        })
        map[ai.ring] = ai
      } else {
        for (var j = 0; j < dest.length; j++) {
          var dj = dest[j]
          if (dj.num === ai.ring) {
            let addphse = this.addPhaseInfo(ai)
            dj.phases.push({...ai, ...addphse})
            break
          }
        }
      }
    }
    phaseRings = JSON.parse(JSON.stringify(dest))
    return phaseRings
  }

  addPhaseInfo (phase) {
    let addphse = {}
    addphse.name = i18n.t('openatccomponents.overview.phase') + phase.id
    addphse.desc = this.getPhaseDescription(phase)
    // 相位锁定选项默认都按照解锁状态显示
    addphse.locktype = 0
    addphse.close = 0
    if (this.crossStatusData !== null && this.crossStatusData.phase) {
      // 如果方案状态相位有close字段，这边就需要对应close状态进相位关断控制的选项里
      let phaseStatus = this.crossStatusData.phase.filter(ele => ele.id === phase.id)[0]
      addphse = {...addphse, ...phaseStatus}
    }
    return addphse
  }

  getPhaseDescription (phaseList) {
    let list = []
    let peddirections = []
    let sidewalkPhaseData = this.getPedPhasePos()
    for (let walk of sidewalkPhaseData) {
      if (phaseList.peddirection) {
        for (let ped of phaseList.peddirection) {
        // if (stg === walk.phaseid) {
          let obj = {}
          obj.name = walk.name
          obj.id = walk.id
          if (ped === walk.id) {
            peddirections.push(obj)
            peddirections = Array.from(new Set(peddirections))
          }
        // }
        }
      } else {
        peddirections = []
      }
    }
    for (let id of phaseList.direction) {
      let obj = {}
      obj.id = id
      obj.peddirection = peddirections
      obj.color = '#454545'
      list.push(obj)
    }
    return list
  }

  getPedPhasePos () {
    // 行人相位信息
    this.sidewalkPhaseData = []
    this.phaseList.forEach((ele, i) => {
      if (ele.peddirection) {
        ele.peddirection.forEach((dir, index) => {
        // 行人相位
          if (this.PhaseDataModel.getSidePos(dir)) {
            this.sidewalkPhaseData.push({
              key: this.CrossDiagramMgr.getUniqueKey('pedphase'),
              phaseid: ele.id, // 相位id，用于对应相位状态
              id: dir,
              name: this.PhaseDataModel.getSidePos(dir).name
            })
          }
        })
      }
    })
    return this.sidewalkPhaseData
  }

  getBusPos () {
    // 公交相位信息
    this.busPhaseData = []
    this.phaseList.forEach((ele, i) => {
      if (ele.controltype) {
        ele.direction.forEach((dir, index) => {
        // 车道相位
          this.busPhaseData.push({
          // key: this.CrossDiagramMgr.getUniqueKey('busphase'),
            phaseid: ele.id, // 相位id，用于对应相位状态
            id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
            name: this.PhaseDataModel.getBusPhasePos(dir).name,
            controltype: ele.controltype
          })
        })
      }
    })
    let result = []
    let obj = {}
    for (var i = 0; i < this.busPhaseData.length; i++) {
      if (!obj[this.busPhaseData[i].phaseid]) {
        result.push(this.busPhaseData[i])
        obj[this.busPhaseData[i].phaseid] = true
      }
    }
    this.busPhaseData = result
    return this.busPhaseData
  }
  getlockData () {
    let stagesTemp = []
    let directionList = []
    let sidewalkPhaseData = this.getPedPhasePos()
    for (let phase of this.phaseList) {
      let tempList = []
      directionList = phase.direction
      let peddirections = []
      if (directionList) {
        for (let walk of sidewalkPhaseData) {
          for (let ped of phase.peddirection) {
          // if (stg === walk.phaseid) {
            let obj = {}
            obj.name = walk.name
            obj.id = walk.id
            if (ped === walk.id) {
              peddirections.push(obj)
              peddirections = Array.from(new Set(peddirections))
            }
          // }
          }
        }
      } else {
        peddirections = []
      }
      tempList = directionList.map(dir => ({
        id: dir,
        color: '#606266',
        controltype: phase.controltype,
        peddirection: peddirections,
        sidewalkPhaseData: sidewalkPhaseData
      }))
      stagesTemp.push(tempList)
    }
    return stagesTemp
  }
  getStageData (datatype) {
    let data = this.crossStatusData
    let stagesTemp = []
    let busPhaseData = this.getBusPos()
    let sidewalkPhaseData = this.getPedPhasePos()
    let stages
    // 按阶段数据处理
    stages = data.stages
    for (let stage of stages) {
      let tempList = []
      let directionList = []
      let currPhaseid = ''
      let stageControType = 0
      let peddirections = []
      let phasetype
      for (let stg of stage) {
        let phaseMode = data.phase.filter(item => item.id === stg)
        phasetype = phaseMode[0].type
        let currPhase = this.phaseList.filter((item) => {
          return item.id === stg
        })[0]
        if (!currPhase) continue
        if (currPhase !== undefined && phaseMode[0].mode !== 1) {
          directionList = [...currPhase.direction, ...directionList]
        }
        if (currPhase.peddirection) {
          for (let walk of sidewalkPhaseData) {
            for (let ped of currPhase.peddirection) {
            // if (stg === walk.phaseid) {
              let obj = {}
              obj.name = walk.name
              obj.id = walk.id
              if (ped === walk.id) {
                peddirections.push(obj)
                peddirections = Array.from(new Set(peddirections))
              }
            // }
            }
          }
        } else {
          peddirections = []
        }
        for (let busPhase of busPhaseData) {
          if (stg === busPhase.phaseid) {
            stageControType = busPhase.controltype
          }
        }
      }
      directionList = [...new Set(directionList)]
      tempList = directionList.map(dir => {
        let temp = {
          id: dir,
          phaseid: currPhaseid,
          color: '#606266',
          controltype: stageControType,
          peddirection: peddirections,
          sidewalkPhaseData: sidewalkPhaseData,
          type: phasetype
        }
        return temp
      })
      if (directionList.length === 0) {
        tempList = [
          {
            id: -1,
            color: '#606266',
            controltype: stageControType,
            peddirection: peddirections,
            sidewalkPhaseData: sidewalkPhaseData,
            type: phasetype
          }
        ]
      }
      stagesTemp.push(tempList)
    }
    this.stagesList = JSON.parse(JSON.stringify(stagesTemp))
    return this.stagesList
  }
  getPhaseLegendData () {
    let data = this.crossStatusData
    let stagesTemp = []
    let busPhaseData = this.getBusPos()
    let sidewalkPhaseData = this.getPedPhasePos()
    let phaseIdList
    phaseIdList = data.phase.map(ele => [ele.id])
    for (let phase of phaseIdList) {
      let tempList = []
      let directionList = []
      let currPhaseid = ''
      let stageControType = 0
      let peddirections = []
      for (let id of phase) {
        let phaseMode = data.phase.filter(item => item.id === id)
        let currPhase = this.phaseList.filter((item) => {
          return item.id === id
        })[0]
        if (!currPhase) return
        if (currPhase !== undefined && phaseMode[0].mode !== 1) {
          directionList = [...currPhase.direction, ...directionList]
          currPhaseid = id
        }
        if (currPhase.peddirection) {
          for (let walk of sidewalkPhaseData) {
            for (let ped of currPhase.peddirection) {
              let obj = {}
              obj.name = walk.name
              obj.id = walk.id
              if (ped === walk.id) {
                peddirections.push(obj)
                peddirections = Array.from(new Set(peddirections))
              }
            }
          }
        } else {
          peddirections = []
        }
        for (let busPhase of busPhaseData) {
          if (id === busPhase.phaseid) {
            stageControType = busPhase.controltype
          }
        }
      }
      directionList = [...new Set(directionList)]
      tempList = directionList.map(dir => {
        let temp = {
          id: dir,
          phaseid: currPhaseid,
          color: '#606266',
          controltype: stageControType,
          peddirection: peddirections,
          sidewalkPhaseData: sidewalkPhaseData
        }
        return temp
      })
      if (directionList.length === 0) {
        tempList = [
          {
            id: -1,
            phaseid: currPhaseid,
            color: '#606266',
            controltype: stageControType,
            peddirection: peddirections,
            sidewalkPhaseData: sidewalkPhaseData
          }
        ]
      }
      stagesTemp.push(tempList)
    }
    this.List = JSON.parse(JSON.stringify(stagesTemp))
    return this.List
  }
}
