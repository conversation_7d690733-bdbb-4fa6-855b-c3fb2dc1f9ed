/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div>
    <el-button type="primary" @click="handleOpenConfigPanel" style="margin: 20px;">打开控制面板</el-button>
    <!-- <el-dialog
      refs="intersectionMapDialog"
      class="abow_dialog"
      :width="dialogWidth"
      :visible.sync="boxVisible"
      :close-on-click-modal="false"
      @close="oncancle"
      append-to-body> -->
      <!-- <scheme-config
        ref="rightpanel"
        :fromPage="fromPage"
        :agentId="agentId"
        :statusData="crossStatusData"
        :phaseList="phaseList2"/> -->
    <!-- </el-dialog> -->

    <div v-if="boxVisible" style="height: 700px;width: 450px;">
      <scheme-config
      ref="rightpanel"
      :agentId="agentId"
      :statusData="crossStatusData"
      :phaseList="phaseList2"
      :realtimeStatusModalvisible="false"
      :isShowBack="false"
      :isShowRecovery="false"/>
    </div>
  </div>
</template>
<script>
import { getTscControl } from '../api/control.js'
import { getMessageByCode } from '../utils/responseMessage.js'
import { getIntersectionInfo } from '../api/template'
import { getTscPhase } from '../api/cross.js'
import {
  setToken
} from '../utils/auth'
export default {
  name: 'democonfig',
  components: {
  },
  data () {
    return {
      fromPage: 2,
      timer: 0,
      phaseList2: [],
      phaseList: [],
      showWalk: [ 1, 2 ],
      sidewalkPhaseData: [{key: 'pedphase1653873194007699', phaseid: 1, id: 1, name: '东人行横道', isshow: false}],
      lockPhaseBtnName: this.$t('openatccomponents.overview.comfirm'),
      agentId: '32050101041121000001',
      Token: 'eyJraWQiOiIxNzAxODMyOTQ3MTUwIiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiJxeXl0aCIsImV4cCI6MTc2OTc4ODgwMCwiaWF0IjoxNzAxMzYwMDAwfQ.RIQpNEkJibL-RYllirDeNLi8G9-t8PecM_m7KlZNQXg',
      Token103: 'eyJraWQiOiIxNzAxODMyOTQ3MTUwIiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiJxeXl0aCIsImV4cCI6MTc2OTc4ODgwMCwiaWF0IjoxNzAxMzYwMDAwfQ.RIQpNEkJibL-RYllirDeNLi8G9-t8PecM_m7KlZNQXg',
      boxVisible: false,
      dialogWidth: '80%',
      crossStatusData: {} // 路口状态数据
    }
  },
  methods: {
    oncancle () {
      this.boxVisible = false
    },
    handleOpenConfigPanel () {
      this.boxVisible = true
      this.$nextTick(() => {
        // this.$refs.rightpanel.selectSpecialModel(22)
      })
    },
    setDialogWidth () {
      var val = document.body.offsetWidth
      const def = 450 // 默认宽度
      if (val < def) {
        this.dialogWidth = '80%'
      } else {
        this.dialogWidth = def + 'px'
      }
    },
    getPhase () {
      getTscPhase(this.agentId).then(data => {
        let res = data.data
        if (!res.success) {
          if (data.data.code === '4002' && data.data.data.errorCode === '4209') {
            let success = data.data.data.content.success
            if (success !== 0) {
              let errormsg = 'openatccomponents.overview.putTscControlError' + success
              this.$message.error(this.$t(errormsg))
              return
            }
          }
          let parrenterror = getMessageByCode(data.data.code, this.$i18n.locale)
          if (data.data.data) {
            // 子类型错误
            let childErrorCode = data.data.data.errorCode
            if (childErrorCode) {
              let childerror = getMessageByCode(data.data.data.errorCode, this.$i18n.locale)
              this.$message.error(parrenterror + ',' + childerror)
            }
          } else {
            this.$message.error(parrenterror)
          }
          return
        }
        if (res.data.data && res.data.data.phaseList) {
          this.phaseList2 = res.data.data.phaseList
        }
      })
    },
    initData () {
      // let iframdevid = getIframdevid()
      this.getIntersectionInfo(this.agentId, '')
      this.getPhase()
      getTscControl(this.agentId).then((data) => {
        if (!data.data.success) {
          let parrenterror = getMessageByCode(data.data.code, this.$i18n.locale)
          if (data.data.data) {
            // 子类型错误
            let childErrorCode = data.data.data.errorCode
            if (childErrorCode) {
              let childerror = getMessageByCode(data.data.data.errorCode, this.$i18n.locale)
              this.$message.error(parrenterror + ',' + childerror)
            }
          } else {
            this.$message.error(parrenterror)
          }
          return
        }
        this.crossStatusData = JSON.parse(JSON.stringify(data.data.data.data))
        console.log('this.crossStatusData:', this.crossStatusData)
      }).catch(error => {
        console.log(error)
      })
    },
    getIntersectionInfo (agentid, id) {
      // 获取路口信息
      getIntersectionInfo(agentid).then(res => {
        this.phaseList = res.data.data.param.phaseList
        console.log('this.phaseList:', this.phaseList)
      })
    },
    setPropsToken (token) {
      // 获取组件外传入的token，便于独立组件调用接口
      if (token && token !== '') {
        setToken(token)
      }
    },
    patternCommitCallback (pattern) {
      console.log(pattern)
    }
  },
  created () {
    this.setDialogWidth()
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  mounted () {
    this.setPropsToken(this.Token)
    this.timer = setInterval(() => {
      this.initData()
    }, 1000 * 2)
    window.onresize = () => {
      return (() => {
        this.setDialogWidth()
      })()
    }
  }
}
</script>
<style lang="scss">
.abow_dialog {
    display: flex;
    justify-content: center;
    align-items: Center;
    overflow: hidden;
    .el-dialog {
        margin: 0 auto !important;
        height: 90%;
        overflow: hidden;
        .el-dialog__body {
            position: absolute;
            left: 0;
            top: 54px;
            bottom: 0;
            right: 0;
            padding: 0;
            z-index: 1;
            overflow-y: auto;
            overflow-x: auto;
        }
    }
}
</style>
