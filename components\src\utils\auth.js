/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
var localStorage = require('../lib/publicjs/localStorage')

const nameSpace = 'openatc_conponents_'
const LoginKey = 'token'
// eslint-disable-next-line camelcase
let Prefix = 'Prefix'
const LoginHost = nameSpace + 'kisshost'
const typeKey = 'type'
const type = 'edge'
const simuKey = 'simu_key'
const language = 'intl_language'
const kisspro = 'kiss_pro'
const theme = 'kiss_theme'
const permissions = 'kiss_permissions'
const userRoles = 'kiss_userRoles'
const isCheckPermission = 'kiss_isCheckPermission'

export function getToken () {
  let key = LoginKey
  if (getPrefix()) {
    key = getPrefix() + LoginKey
    console.log('get tkey:', key)
  }
  return localStorage.getStorage(key)
}

export function setToken (token, prefix) {
  let key = LoginKey
  if (prefix) {
    setPrefix(prefix)
    key = getPrefix() + LoginKey
    console.log('set tkey:', key)
  }
  return localStorage.setStorage(key, token)
}

export function getPrefix () {
  return localStorage.getStorage(Prefix)
}

export function setPrefix (prifix) {
  return localStorage.setStorage(Prefix, prifix)
}

export function SetSimuUserKey (key) {
  return localStorage.setStorage(simuKey, key)
}

export function removeToken () {
  return localStorage.setStorage(LoginKey, '')
}

export function getStageTypes () {
  let ret = localStorage.getStorage('isRing')
  if (ret === undefined || ret === null) {
    return ''
  }
  return ret
}

export function getHost () {
  let ret = localStorage.getStorage(LoginHost)
  if (ret === undefined || ret === null) {
    return ''
  }
  return ret
}

export function setHost (host) {
  return localStorage.setStorage(LoginHost, host)
}

export function getLanguage () {
  let intl = localStorage.getStorage(language)
  if (intl === undefined || intl === null || intl === '') {
    return ''
  }
  return intl
}

export function setLanguage (lan) {
  return localStorage.setStorage(language, lan)
}

export const setType = () => {
  window[typeKey] = type
}

export const getType = () => {
  return window[typeKey]
}
// 从缓存获取设备ID
export const getIframdevid = () => {
  var iframdevid = localStorage.getStorage('iframdevid')
  if (iframdevid === '' || iframdevid === null || iframdevid === undefined || iframdevid === 'undefined') {
    iframdevid = 0
  }
  return iframdevid
}

export const setIframdevid = (v) => {
  return localStorage.setStorage('iframdevid', v)
}

export function setKiss (v) {
  return localStorage.setStorage(kisspro, v)
  // return localStorage.setItem(LoginUserName, username)
}

export function getKiss () {
  return localStorage.getStorage(kisspro)
}

export function getTheme () {
  let th = localStorage.getStorage(theme)
  if (th === undefined || th === null || th === '') {
    return 'dark'
  }
  return th
}

export function setTheme (th) {
  return localStorage.setStorage(theme, th)
}

export function getIsCheckPermission () {
  return localStorage.getStorage(isCheckPermission)
}

export function setIsCheckPermission (val) {
  return localStorage.setStorage(isCheckPermission, val)
}
export function getPermissions () {
  let intl = localStorage.getStorage(permissions)
  if (intl === undefined || intl === null || intl === '') {
    return []
  }
  return intl
}
export function setPermissions (lan) {
  return localStorage.setStorage(permissions, lan)
}

export function getUserRoles () {
  let intl = localStorage.getStorage(userRoles)
  if (intl === undefined || intl === null || intl === '') {
    return []
  }
  return intl
}
export function setUserRoles (lan) {
  return localStorage.setStorage(userRoles, lan)
}

export function hasPermission (permissionStr) {
  let res = false
  let isCheckPermission = getIsCheckPermission()
  if (!isCheckPermission) { // 默认不校验
    res = true
    return res
  }
  if (!permissionStr) {
    res = true
    return res
  }
  let roles = getUserRoles()
  if (roles.includes('superadmin') || roles.includes('admin')) {
    res = true
  } else {
    let permissions = getPermissions()
    if (permissions === '*' || permissions.includes(permissionStr)) {
      res = true
    } else {
      let perArr = []
      let eleArr = permissionStr.replace('*', '').split(':')
      let item = ''
      for (let ele of eleArr) {
        item = item.replace(':*', ':') + ele + ':*'
        perArr.push(item)
        if (permissions.includes(item)) {
          res = true
          break
        }
      }
    }
  }
  return res
}
