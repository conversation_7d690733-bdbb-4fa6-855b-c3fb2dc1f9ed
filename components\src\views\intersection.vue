<template>
  <div>
    <intersection-with-interface :AgentId="agentId" />
    <h2 class="text">实时通道路口图</h2>
    <!-- <channel-realtime-intersection
      :agentId="this.agentId"
      :roadDirection="roadDirection"
      :channelRealtimeStatusData="channelRealtimeStatusData"
    /> -->

    <h2>基础路口图方向显示、选择组件</h2>
    <!-- channelType是按通道显示方向；不传按相位显示方向 -->
    <!-- clickMode是否开启方向选择功能 -->
    <el-button @click="clear"></el-button>
    <custom-intersection-base-map
      ref="intersectionMap3"
      clickMode
      channelType
      :agentId="this.agentId"
      :choosedDirection="choosedDirection"
      :choosedPedDirection="choosedPedDirection"/>

    <!-- 按通道获取相位的方向选择列表，特殊路口显示列表，普通路口显示模版路口图 -->
    <h2 class="text">非第三方配置路口图(按通道显示相位方向)</h2>
    <!-- <intersection-direction-selection
      :agentId="this.agentId"
      :choosedDirection="[14,6,13,8]"
      :choosedPedDirection="[1]"
      :roadDirection="roadDirection"
      @handleClickCrossIcon="handleClickCrossIcon" />

      <h2 class="text">第三方配置路口图(按路口相位配置显示相位方向)</h2>
      <div>
        <intersection-direction-selection
        :agentId="this.agentId"
        thirdSignal
        :choosedDirection="[5,6,7,8,13]"
        :choosedPedDirection="[1,2]"
        :roadDirection="roadDirection"
        @handleClickCrossIcon="handleClickCrossIcon" />
      </div> -->

      <!-- 显示按通道获取相位的模版路口图 -->
    <h2 class="text">显示配置的相位路口图</h2>
    <!-- <intersection-base-map
            ref="intersectionMap"
            :crossStatusData="crossStatusData"
            agentId="1059"
            isVipRoute
            :choosedDirection="[1,2,3]"
            :choosedPedDirection="[3,4]"
            :roadDirection="roadDirection" /> -->
      <el-button @click="refresh">刷新特勤路口底图</el-button>
      <intersection-direction-selection
            ref="selectMap"
            :agentId="1059"
            :clickMode="false"
            :choosedDirection="[14,6,13,8]"
            :choosedPedDirection="[1]"
            :roadDirection="roadDirection" />

      <!-- <button @click="lockPhase" >相位锁定</button><br/>
      <button @click="unlockPhase" >解锁相位</button><br/>
      <button @click="changeControlPattern" >改变控制方案</button><br/>
      <button @click="getPhaseInfo" >获取相位参数信息</button><br/>
      <button @click="getControlInfo" >获取当前方案信息</button>
      <button @click="clearInterVals" >清除定时器</button> -->
    <!-- <el-button type="primary" @click="handleOpenConfigPanel" style="margin: 20px;">打开路口图面板</el-button>
    <el-dialog
      refs="intersectionMapDialog"
      class="abow_dialog"
      :width="dialogWidth"
      :visible.sync="boxVisible"
      :close-on-click-modal="false"
      @close="oncancle"
      append-to-body> -->
      <!-- <intersection-with-interface
        ref="intersection"
        :AgentId="agentId"
        :reqUrl="reqUrl"
        :Token="Token"
        :roadDirection="roadDirection"
        :modeName="modeName"
        :controlName="controlName"
        :isShowMode="isShowMode"
        :isShowInterval="isShowInterval"
        :isShowMessage ="isShowMessage"
        :isShowPatternStatus="isShowPatternStatus"
        :isShowIntersection="isShowIntersection"
        :isShowStages="isShowStages"
        @getTscControl="getTscControl"
        @registerMessage="registerMessage"
        @queryDevice="queryDevice"
        @onPhaseChange="onPhaseChange"
        @onSelectStages="onSelectStages">
      </intersection-with-interface> -->
    <!-- </el-dialog> -->
  <!-- <div class="layout-container2">
      <h2 style="color: #fff;">路口图</h2>
      <intersection-with-interface
        ref="intersection"
        :AgentId="agentId"
        :reqUrl="reqUrl"
        :Token="Token"
        :roadDirection="roadDirection"
        :modeName="modeName"
        :controlName="controlName"
        :isShowState="isShowState"
        :isShowMode="isShowMode"
        :isShowInterval="isShowInterval"
        :isShowMessage ="isShowMessage"
        :isShowPatternStatus="isShowPatternStatus"
        :isShowIntersection="isShowIntersection"
        :isShowStages="isShowStages"
        @getTscControl="getTscControl"
        @registerMessage="registerMessage"
        @queryDevice="queryDevice"
        @onPhaseChange="onPhaseChange"
        @onSelectStages="onSelectStages">
      </intersection-with-interface> -->
      <!-- <Stages :stagesList="stagesList"
              :currentStage="currentStage"
              @onSelectStages="onSelectStages"></Stages> -->
          <div style="height: 100px;">
            <PhaseLegend :crossStatusData="tscControlData"
              :phaseList="phaseList"
              showDataType="phase"
              :noClick="true"
              :isShowCurrentStage="false"
              StageWidth="46px"
              StageHeight="52px"
              dirWidth="46px"
              dirHeight="52px"
              dirWidths="46px"
              dirHeights="52px"
              :showStyle="{top:'2PX',left:'9px'}"
              :showStyles="{top:'0PX',left:'7px'}"
              ></PhaseLegend>
          </div>

      <!-- <h2 style="color: #fff;">渠化图</h2>
      <channelization-with-interface
        ref="channelization"
        :AgentId="agentId"
        :reqUrl="reqUrl"
        :Token="Token"
        @getTscControl="getTscControl"
        @onPhaseChange="onPhaseChange"/>

    <h2 style="color: #fff;">流量统计渠化路口图</h2>
      <channelization-flow-statistic
      :AgentId="agentId"
      :phasesStatisticsList="phasesStatisticsList"
      bcgColor="#009900"
      :customText="customText"
      textFontSize="50"/> -->

    <!-- </div> -->
  </div>
</template>
<script>
import {
  setToken
} from '../utils/auth'
import IntersectionWithInterface from '../kisscomps/components/IntersectionWithInterface'
import ChannelizationWithInterface from '../kisscomps/components/ChannelizationWithInterface/ChannelizationWithInterface'
import {
  getTscPhase
} from '../api/cross.js'
import {
  getTscControl
} from '../api/control.js'
export default {
  name: 'demo',
  data () {
    return {
      roadDirection: 'right',
      // reqUrl: 'http://**************:10003/openatc',
      agentId: '1059',
      // agentId: '13013',
      // agentId: '12007_390',
      // agentId: '12014',
      reqUrl: 'http://**************:10003/openatc',
      Token: 'eyJraWQiOiIxNzQ4MjM2MTEwNjg1IiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTc0ODIzOTcxMCwiaWF0IjoxNzQ4MjMyNTEwfQ.wUT3laplbdjDofqbItQHJXAvAD0MQz6oU-1StSvj3bc',
      // agentId: '30003-352',
      // reqUrl: 'https://kints-dev.devdolphin.com/openatc',
      // Token: 'eyJraWQiOiIxNjUwNTA5MDI2ODk2IiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJzdWIiOiJ4aWFvbWluZyIsImV4cCI6MTczNjkwOTAyNiwiaWF0IjoxNjUwNTA5MDI2fQ.-s4T-uMRmB2zf9yer87USKQXLY1a12Zq5lCOnqjNmfA',
      boxVisible: false,
      dialogWidth: '80%',
      modeName: '交警遥控',
      controlName: '步进',
      isShowState: true,
      isShowMode: true,
      isShowMessage: true,
      isShowPatternStatus: false,
      isShowIntersection: true,
      isShowInterval: true,
      isShowStages: true,
      customText: 'A',
      phasesStatisticsList: [
        {
          'phaseno': 1,
          'time': '2020-08-09 15:44:22',
          'phasestatistics': {
            'totalflow': 49,
            'averageflow': 12,
            'saturation': 1,
            'occupyrate': 69,
            'congestionindex': 'A',
            'greenusage': 10
          }
        },
        {
          'phaseno': 2,
          'time': '2020-08-09 15:44:22',
          'phasestatistics': {
            'totalflow': 62,
            'averageflow': 31,
            'saturation': 3,
            'occupyrate': 98,
            'congestionindex': 'F',
            'greenusage': 10
          }
        }
      ],
      crossStatusData: {},
      crossStatusDataMock:
        {
          'agentid': '12008_ticp',
          'operation': 'get-request',
          'infotype': 'status/pattern',
          'data': {
            'phase': [
              {
                'id': 1,
                'type': 1,
                'pedtype': 1
              },
              {
                'id': 3,
                'type': 1,
                'pedtype': 1
              },
              {
                'id': 4,
                'type': 1,
                'pedtype': 1
              },
              {
                'id': 6,
                'type': 3,
                'pedtype': 3
              }
            ],
            'mode': 0,
            'control': 5,
            'patternid': 2,
            'cycle': 141,
            'stages': [
              [
                1
              ],
              [
                4,
                3
              ],
              [
                6
              ]
            ],
            'stages_len': [
              70,
              25,
              46
            ],
            'stages_seq': [
              1,
              2,
              3
            ],
            'current_stage': 3,
            'current_stagecd': 15,
            'offset': 0,
            'current_phase': [
              6
            ],
            'next_phase': [
              1
            ],
            'curTime': 127
          }
        },
      channelRealtimeStatusData: {
        'channellamp': [
          {
            'id': 1,
            'light': 1
          },
          {
            'id': 2,
            'light': 3
          },
          {
            'id': 3,
            'light': 1
          },
          {
            'id': 4,
            'light': 1
          },
          {
            'id': 5,
            'light': 3
          },
          {
            'id': 6,
            'light': 1
          },
          {
            'id': 7,
            'light': 3
          },
          {
            'id': 8,
            'light': 3
          },
          {
            'id': 9,
            'light': 1
          },
          {
            'id': 10,
            'light': 1
          },
          {
            'id': 11,
            'light': 3
          }
        ]
      },
      tscControlData: {},
      phaseList: [],
      choosedDirection: [1, 5],
      choosedPedDirection: [3]
    }
  },
  components: {
    IntersectionWithInterface,
    ChannelizationWithInterface
  },
  methods: {
    oncancle () {
      this.boxVisible = false
    },
    handleOpenConfigPanel () {
      this.boxVisible = true
    },
    setDialogWidth () {
      var val = document.body.offsetWidth
      const def = 1200 // 默认宽度
      if (val < def) {
        this.dialogWidth = '80%'
      } else {
        this.dialogWidth = def + 'px'
      }
    },
    async lockPhase (reqData) {
      console.log('lock')
      reqData = {
        'mode': 0,
        'control': 4,
        'terminal': 1,
        'delay': null,
        'duration': null,
        'value': 1
      }
      let res = await this.$refs.intersection.lockPhase(reqData)
      console.log(res)
      console.log(reqData)
    },
    async unlockPhase (reqData) {
      console.log('unlock')
      reqData = {
        'mode': 0,
        'control': 0,
        'terminal': 1,
        'delay': null,
        'duration': null,
        'value': 0
      }
      let res = await this.$refs.intersection.unlockPhase(reqData)
      console.log(res)
      console.log(reqData)
    },
    async changeControlPattern (reqData) {
      console.log('changeControlPattern')
      reqData = {
        'mode': 0,
        'control': 1,
        'terminal': 1,
        'delay': null,
        'duration': null,
        'value': 0
      }
      let res = await this.$refs.intersection.changeControlPattern(reqData)
      console.log(res)
      console.log(reqData)
    },
    async getPhaseInfo () {
      console.log('getPhaseInfo')
      let res = await this.$refs.intersection.getPhaseInfo()
      console.log(res)
      return res
    },
    async getControlInfo () {
      console.log('getControlInfo')
      let res = await this.$refs.intersection.getControlInfo()
      console.log(res)
      return res
    },
    onPhaseChange (res, index) {
      console.log('onPhaseChange:')
      console.log(res, index)
      this.stagesList = res
      // this.currentStage = index
    },
    getTscControl (res) {
      console.log('getTscControl:', res)
      // let control = res.data.data.control
      // let mode = res.data.data.mode
      // console.log("control,mode:",control,mode)
      // this.controlName = control
      // this.modeName = mode
    },
    getTscControlData () {
      this.tscControlData = {}
      getTscControl(this.agentId).then((data) => {
        this.tscControlData = data.data.data.data
      }).catch(error => {
        console.log(error)
      })
    },
    getCurPhaseDirection () {
      getTscPhase(this.agentId).then(res => {
        this.phaseList = res.data.data.data.phaseList
      })
    },
    registerMessage (res) {
      console.log('registerMessage:', res)
    },
    queryDevice (res) {
      console.log('queryDevice:', res)
    },
    clearInterVals () {
      this.$refs.intersection.clearInterVals()
    },
    onSelectStages (res) {
      console.log('onSelectStages')
      console.log(res)
    },
    setPropsToken (token) {
      // 获取组件外传入的token，便于独立组件调用接口
      if (token && token !== '') {
        setToken(token)
      }
    },
    handleClickCrossIcon (allChoosedDir, curClickedPhase) {
      console.log('handleClickCrossIcon', allChoosedDir, curClickedPhase)
    },
    refresh () {
      this.$refs.selectMap.refresh()
    },
    clear () {
      this.choosedDirection = []
      this.choosedPedDirection = []
      this.$refs.intersectionMap3.resetCrossDiagram()
    }
  },
  created () {
    this.setDialogWidth()
  },
  mounted () {
    // let _this = this
    // setTimeout(function () {
    //   alert('Hello')
    //   _this.agentId = 'scats1'
    // }, 5 * 1000)
    this.setPropsToken(this.Token)
    window.onresize = () => {
      return (() => {
        this.setDialogWidth()
      })()
    }
    this.getTscControlData()
    this.getCurPhaseDirection()
    // setTimeout(() => {
    //   this.crossStatusData = this.crossStatusDataMock.data
    // }, 1000)
  },
  destroyed () {
  }
}
</script>
<style lang='scss'>
.layout-container {
  width: 800PX;
}
.layout-container1 {
  width: 242PX;
  height: 176PX;
  border: 1PX solid green;
}
.layout-container2 {
  /* height: 600px; */
  width: 1020px;
  border: 1PX solid yellow;
  position: relative;
}
.abow_dialog {
    display: flex;
    justify-content: center;
    align-items: Center;
    overflow: hidden;
    .el-dialog {
        margin: 0 auto !important;
        height: 90%;
        overflow: hidden;
        .el-dialog__body {
            position: absolute;
            left: 0;
            top: 54px;
            bottom: 0;
            right: 0;
            padding: 0;
            z-index: 1;
            overflow-y: auto;
            overflow-x: auto;
        }
    }
}
</style>
<style lang="scss" scoped>
  .text {
    color: #fff;
  }
</style>
