/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="app-container plan-container">
    <div class="tabs-style">
      <el-tabs
        v-model="curTabsValue"
        type="card"
        editable
        @edit="handleTabsEdit"
        :before-leave="beforeLeave"
      >
        <el-tab-pane
          v-for="item in planList"
          :key="item.id"
          :label="item.desc"
          :name="String(item.id)"
        >
          <TabPane
            :plan="item.plan"
            :planid="item.id"
            :planname="item.desc"
            :coordinate="item.coordinate"
          />
        </el-tab-pane>
        <el-tab-pane key="add" name="add" :closable="false">
          <template #label>
            <span style="padding: 8px; font-size: 20px; font-weight: bold;">+</span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import TabPane from './tabPane.vue'

// 定义接口类型
interface PlanItem {
  id: number
  desc: string
  plan: any[]
  coordinate: number
}

// 组合式API
const store = useStore()

// 响应式数据
const curTabsValue = ref('1')
const tabIndex = ref(0)
const dinations = ref(0)
const isAddTab = ref(false)
const isDeleteTab = ref(false)

// 全局参数模型
let globalParamModel: any = null

// 计算属性
const planList = computed(() => store.state.globalParam.tscParam.planList)

// 监听器
watch(planList, () => {
  init()
})

// 初始化
const init = () => {
  const planListData = globalParamModel.getParamsByType('planList')
  if (planListData.length > 0) {
    // 保证增加tab页后，当前页展示新加的tab内容
    curTabsValue.value = isAddTab.value || isDeleteTab.value
      ? curTabsValue.value
      : String(planListData[0].id)

    if (isAddTab.value) {
      isAddTab.value = false
    }
    if (isDeleteTab.value) {
      isDeleteTab.value = false
    }
  }
}

// 标签编辑处理
const handleTabsEdit = (targetName: string, action: string) => {
  const planListData = globalParamModel.getParamsByType('planList')

  if (action === 'add') {
    if (planListData.length >= 16) {
      ElMessage.error('最多只能创建16条数据!')
      return
    }
    addTab()
  }

  if (action === 'remove') {
    const tabs = JSON.parse(JSON.stringify(planListData))
    let activeName = curTabsValue.value

    ElMessageBox.confirm(
      '确认删除此计划？',
      '提示',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      if (activeName === targetName) {
        tabs.forEach((tab: PlanItem, index: number) => {
          if (String(tab.id) === targetName) {
            const nextTab = tabs[index + 1] || tabs[index - 1]
            if (nextTab) {
              activeName = String(nextTab.id)
            }
          }
        })
      }
      curTabsValue.value = activeName
      isDeleteTab.value = true
      store.getters.tscParam.planList = tabs.filter((tab: PlanItem) => String(tab.id) !== targetName)
    }).catch(() => {
      ElMessage({
        type: 'info',
        message: '删除取消！'
      })
    })
  }
}

// 获取可用的ID（范围在1-255之间，且是与其他id不能重复的最小的值）
const getIdOfByte = (): number => {
  const planListData = globalParamModel.getParamsByType('planList')
  if (planListData.length === 0) {
    return 1
  } else {
    const list: number[] = []
    for (const plan of planListData) {
      list.push(plan.id)
    }
    for (let i = 1; i < 255; i++) {
      if (!list.includes(i)) {
        return i
      }
    }
    return 1 // 默认返回1
  }
}

// 添加标签页
const addTab = () => {
  const planItem: PlanItem = {
    id: getIdOfByte(),
    desc: `计划${getIdOfByte()}`,
    plan: [],
    coordinate: dinations.value
  }

  globalParamModel.addParamsByType('planList', planItem)
  curTabsValue.value = String(planItem.id)
  isAddTab.value = true
}

// 活动标签切换时触发
const beforeLeave = (currentName: string) => {
  // 如果name是add，则什么都不触发
  if (currentName === 'add') {
    addTab()
    return false
  }
  return true
}

// 生命周期钩子
onMounted(() => {
  globalParamModel = store.getters.globalParamModel
  init()
})
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
}

.plan-container {
  height: 100%;
}

.tabs-style {
  height: 100%;

  :deep(.el-tabs__new-tab) {
    display: none;
  }

  :deep(.el-tabs__content) {
    height: calc(100% - 40px);
    overflow: auto;
  }

  :deep(.el-tab-pane) {
    height: 100%;
  }
}

// 隐藏增加tab的右上角删除图标
:deep(#tab-add .el-icon-close) {
  display: none;
}
</style>
