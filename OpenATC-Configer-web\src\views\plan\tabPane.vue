/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="plan-table" ref="planTableRef">
    <div style="display: inline-block;" ref="ignoreClickOutsideRef">
      <el-button
        style="margin-bottom: 10px"
        type="primary"
        @click="onAdd"
      >
        {{ $t('edge.common.add') }}
      </el-button>
      <el-button
        style="margin-bottom: 10px"
        type="primary"
        @click="editName"
      >
        {{ $t('edge.plan.editname') }}
      </el-button>
      <el-button
        style="margin-bottom: 10px"
        type="primary"
        @click="handleSort"
      >
        {{ $t('edge.plan.sort') }}
      </el-button>
      <span class="coordination">
        {{ $t('edge.plan.Coordination') }}:
        <el-switch
          v-model="coorDinations"
          :active-value="1"
          :inactive-value="0"
          @change="changeDination"
        />
      </span>
    </div>
    <div ref="tableContainerRef">
      <el-table
        v-if="visible"
        ref="planTableRef"
        :data="plan"
        v-loading="listLoading"
        element-loading-text="Loading"
        fit
        highlight-current-row
        :max-height="tableHeight"
        id="footerBtn"
        @row-click="handleRowClick"
      >
        <!-- <el-table-column align="center" label="ID" width="60">
          <template #default="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column> -->
        <el-table-column
          :label="$t('edge.plan.time')"
          min-width="190"
          align="center"
        >
          <template #default="scope">
            <el-row :gutter="4">
              <el-col :span="10">
                <el-select
                  v-model="scope.row.hour"
                  filterable
                  style="width: 80px"
                  size="small"
                  @focus="handleRowClick(scope.row)"
                >
                  <el-option
                    v-for="item in hoursOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-col>
              <el-col :span="2" style="text-align: center">
                <span>:</span>
              </el-col>
              <el-col :span="10">
                <el-autocomplete
                  v-model="scope.row.minute"
                  style="width: 80px"
                  class="inline-input"
                  size="small"
                  :maxlength="2"
                  :fetch-suggestions="querySearch"
                  @select="handleSelect"
                  @change="(val) => onMinuteChange(val, scope.row)"
                />
              </el-col>
            </el-row>
          </template>
        </el-table-column>
        <!-- <el-table-column :label="$t('edge.plan.pattern')" min-width="100" align="center">
          <template #default="scope">
            <el-select v-model="scope.row.pattern" size="small" @change="doChange(scope.row)">
              <el-option
                v-for="item in patternOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column> -->
        <el-table-column
          :label="$t('edge.plan.controltype')"
          min-width="100"
          align="center"
        >
          <template #default="scope">
            <el-select
              v-model="scope.row.control"
              size="small"
              @change="doChange(scope.row)"
              @focus="handleRowClick(scope.row)"
            >
              <el-option
                v-for="item in controlOptions"
                :key="item.value"
                :label="$t('edge.plan.ControlOption' + item.value)"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('edge.plan.pattern')"
          min-width="100"
          align="center"
        >
          <template #default="scope">
            <el-select
              v-model="scope.row.pattern"
              size="small"
              :disabled="handleControl(scope.row)"
              @focus="handleRowClick(scope.row)"
            >
              <el-option
                v-for="item in patternOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('edge.plan.cycle')"
          align="center"
        >
          <template #default="scope">
            <span v-if="scope.row.pattern">
              {{ getPatternCycle(scope.row.pattern) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('edge.plan.operation')"
          align="center"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="handleDelete(scope.$index)"
            >
              {{ $t('edge.common.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Ref } from 'vue'

// 定义接口类型
interface PlanItem {
  id: number
  hour: number
  minute: number | string
  control: number
  pattern?: number
}

interface OptionItem {
  value: number
  label: string
  cycle?: number
}

interface MinuteOption {
  value: number
  label: string
}

// Props定义
interface Props {
  plan: PlanItem[]
  planid: number
  planname: string
  coordinate: number
}

const props = defineProps<Props>()

// 组合式API
const store = useStore()
const { t } = useI18n()

// 响应式引用
const planTableRef = ref()
const tableContainerRef = ref()
const ignoreClickOutsideRef = ref()
const tableHeight = ref(700)
const coorDinations = ref(props.coordinate)
const screenHeight = ref(window.innerHeight)
const id = ref(1)
const listLoading = ref(false)
const controlStatus = ref(true)
const visible = ref(true)
const curClickedRow: Ref<PlanItem | null> = ref(null)

// 计算属性
const planList = computed(() => store.state.globalParam.tscParam.planList)
const sortPlanList = computed(() => store.state.globalParam.sortPlanList)

// 小时选项
const hoursOptions: OptionItem[] = Array.from({ length: 24 }, (_, i) => ({
  value: i,
  label: i.toString().padStart(2, '0')
}))

// 分钟选项（只包含特定的分钟值）
const minuteOptions: MinuteOption[] = [
  { value: 0, label: '00' },
  { value: 5, label: '05' },
  { value: 10, label: '10' },
  { value: 15, label: '15' },
  { value: 20, label: '20' },
  { value: 25, label: '25' },
  { value: 30, label: '30' },
  { value: 35, label: '35' },
  { value: 40, label: '40' },
  { value: 45, label: '45' },
  { value: 50, label: '50' },
  { value: 55, label: '55' },
  { value: 59, label: '59' }
]

// 方案选项（响应式）
const patternOptions = ref<OptionItem[]>([])

// 控制选项
const controlOptions: OptionItem[] = [
  { value: 5 },
  { value: 10 },
  { value: 6 },
  { value: 1 },
  { value: 2 },
  { value: 3 },
  { value: 9 },
  { value: 12 },
  { value: 18 },
  { value: 19 }
]

// 全局参数模型
let globalParamModel: any = null

// 初始化方法
const initializePatternOptions = () => {
  globalParamModel = store.getters.globalParamModel
  const patternList = globalParamModel.getParamsByType('patternList')

  patternOptions.value = patternList.map((pattern: any) => ({
    value: pattern.id,
    label: pattern.desc || `${t('edge.pattern.pattern')}${pattern.id}`,
    cycle: pattern.cycle
  }))

  // 清理无效的pattern
  props.plan.forEach((planItem: PlanItem) => {
    const patternValues = patternOptions.value.map(p => p.value)
    if (planItem.pattern && !patternValues.includes(planItem.pattern)) {
      delete planItem.pattern
    }
  })

  // 添加协调控制选项
  planList.value.forEach((item: any) => {
    if (item.coordinate === 1 && item.id === props.planid) {
      controlOptions.push({ value: 0 })
    }
  })

  increaseId()
}

// 设置表格最大高度
const setTableMaxHeight = () => {
  nextTick(() => {
    if (planTableRef.value) {
      tableHeight.value = planTableRef.value.offsetHeight - 50
      window.onresize = () => {
        if (planTableRef.value) {
          tableHeight.value = planTableRef.value.offsetHeight - 50
        }
      }
    }
  })
}

// 点击外部处理
const handleClickOutside = (event: Event) => {
  if (tableContainerRef.value && !tableContainerRef.value.contains(event.target as Node)) {
    if (ignoreClickOutsideRef.value && ignoreClickOutsideRef.value.contains(event.target as Node)) return
    handleRowClick()
  }
}

// 监听器
watch(screenHeight, () => {
  const footerBtn = document.querySelector('#footerBtn') as HTMLElement
  if (footerBtn) {
    tableHeight.value = window.innerHeight - footerBtn.offsetTop - 150
  }
})

watch(sortPlanList, (val) => {
  if (val) {
    sortAllPlan()
  }
})

// 方法定义
const querySearch = (query: string, cb: (results: MinuteOption[]) => void) => {
  const queryString = query.toString()
  const results = queryString
    ? minuteOptions.filter(createFilter(queryString))
    : minuteOptions
  cb(results)
}

const createFilter = (queryString: string) => {
  return (option: MinuteOption) => {
    return option.label.toLowerCase().indexOf(queryString.toLowerCase()) === 0
  }
}

const handleSelect = (item: MinuteOption) => {
  console.log(item)
}

const onMinuteChange = (val: string, row: PlanItem) => {
  let res = Number(val) || 0
  if (res < 0 || res > 59) {
    res = 0
  }
  row.minute = res
}

const changeDination = (val: number) => {
  if (val === 1) {
    controlOptions.push({ value: 0 })
  } else {
    const index = controlOptions.findIndex(i => i.value === 0)
    if (index > -1) {
      controlOptions.splice(index, 1)
    }
  }
  planList.value.forEach((item: any) => {
    if (item.id === props.planid) {
      item.coordinate = val
    }
  })
}

const increaseId = () => {
  // 实现id在之前的基础上寻找最小的
  const planIdList = props.plan.map(ele => ele.id)
  const i = props.plan.length - 1
  if (i >= 0) {
    for (let j = 1; j <= 48; j++) {
      if (!planIdList.includes(j)) {
        id.value = j
        return
      }
    }
  }
}

const handleDelete = (index: number) => {
  props.plan.splice(index, 1)
  ElMessage({
    type: 'success',
    message: t('edge.common.deletesucess')
  })
}

const onAdd = () => {
  increaseId()
  if (props.plan.length >= 48) {
    ElMessage.error(t('edge.plan.mostdata'))
    return
  }

  const newPlanItem: PlanItem = {
    id: id.value,
    hour: 0,
    minute: 0,
    control: 5
  }

  if (curClickedRow.value) {
    const insertIndex = props.plan.findIndex(row => row.id === curClickedRow.value!.id)
    props.plan.splice(insertIndex + 1, 0, newPlanItem)
    handleRowClick(curClickedRow.value)
  } else {
    props.plan.push(newPlanItem)
  }
}

const compareProperty = (property: keyof PlanItem) => {
  return (a: PlanItem, b: PlanItem) => {
    const value1 = a[property] as number
    const value2 = b[property] as number
    return value1 - value2
  }
}

const doChange = (row: PlanItem) => {
  if (row.control === 1 || row.control === 2 || row.control === 3) {
    if (row.pattern !== undefined) {
      delete row.pattern
    }
  }
}

const handleControl = (row: PlanItem) => {
  return (
    row.control === undefined ||
    row.control === 0 ||
    row.control === 1 ||
    row.control === 2 ||
    row.control === 3
  )
}

const editName = () => {
  ElMessageBox.prompt(t('edge.plan.editcontext'), t('edge.plan.tip'), {
    confirmButtonText: t('edge.plan.ok'),
    cancelButtonText: t('edge.plan.cancel'),
    inputValue: props.planname,
    inputValidator: (value: string) => {
      if (!value || value.replace(/\s/g, '') === '') {
        return t('edge.plan.plannamerequired')
      }

      const inputValue = value.replace(/\s/g, '')
      const planListData = globalParamModel.getParamsByType('planList')

      for (const obj of planListData) {
        let curDesc = obj.desc
        if (curDesc) {
          curDesc = curDesc.replace(/\s/g, '')
        }
        if (curDesc === inputValue && curDesc !== props.planname) {
          return t('edge.plan.plannamerepeated')
        }
      }
      return true
    }
  }).then(({ value }) => {
    planList.value.forEach((plan: any) => {
      if (plan.id === props.planid) {
        plan.desc = value
      }
    })
  }).catch(() => {
    // 取消操作
  })
}

const handleSort = () => {
  planList.value.forEach((plan: any) => {
    if (plan.id === props.planid) {
      const curPlanList = plan.plan
      // 冒泡排序
      for (let i = curPlanList.length - 1; i > 0; i--) {
        for (let j = 0; j < i; j++) {
          if (curPlanList[j].hour > curPlanList[j + 1].hour) {
            [curPlanList[j], curPlanList[j + 1]] = [curPlanList[j + 1], curPlanList[j]]
          } else if (curPlanList[j].hour === curPlanList[j + 1].hour) {
            if (curPlanList[j].minute > curPlanList[j + 1].minute) {
              [curPlanList[j], curPlanList[j + 1]] = [curPlanList[j + 1], curPlanList[j]]
            }
          }
        }
      }
    }
  })

  // 解决select聚焦效果未取消
  visible.value = false
  nextTick(() => {
    visible.value = true
  })
}

const handleRowClick = (row?: PlanItem) => {
  if (row && planTableRef.value) {
    planTableRef.value.setCurrentRow(row)
    curClickedRow.value = row
  } else {
    curClickedRow.value = null
  }
}

const sortAllPlan = () => {
  // 下载前排序刷新表格
  visible.value = false
  nextTick(() => {
    visible.value = true
    store.dispatch('SetPlanListOrder', false)
  })
}

// 获取方案周期
const getPatternCycle = (patternId: number) => {
  const pattern = patternOptions.value.find(item => item.value === patternId)
  return pattern?.cycle || ''
}

// 生命周期钩子
onMounted(() => {
  initializePatternOptions()
  setTableMaxHeight()
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.plan-table {
  height: 100%;
}

.coordination {
  margin-left: 20px;
  font-weight: bold;
}

.inline-input {
  width: 80px;
}
</style>
